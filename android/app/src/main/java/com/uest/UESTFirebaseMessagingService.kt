package com.uest

import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.PendingIntent
import android.content.Context
import android.content.Intent
import android.os.Build
import android.util.Log
import androidx.core.app.NotificationCompat
import com.google.firebase.messaging.FirebaseMessagingService
import com.google.firebase.messaging.RemoteMessage

class UESTFirebaseMessagingService : FirebaseMessagingService() {

    companion object {
        private const val TAG = "UESTFCMService"
        private const val CHANNEL_ID = "uest_notifications"
        private const val CHANNEL_NAME = "UEST Notifications"
        private const val CHANNEL_DESCRIPTION = "Notifications for UEST app"
    }

    override fun onCreate() {
        super.onCreate()
        createNotificationChannel()
        Log.d(TAG, "UESTFirebaseMessagingService created")
    }

    override fun onMessageReceived(remoteMessage: RemoteMessage) {
        super.onMessageReceived(remoteMessage)

        Log.d(TAG, "=== FCM Message Received ===")
        Log.d(TAG, "From: ${remoteMessage.from}")
        Log.d(TAG, "Message ID: ${remoteMessage.messageId}")
        Log.d(TAG, "Data payload: ${remoteMessage.data}")
        Log.d(TAG, "Notification payload: ${remoteMessage.notification}")

        // Always show notification for background/killed app state
        val title = remoteMessage.notification?.title ?: remoteMessage.data["title"] ?: "UEST"
        val body = remoteMessage.notification?.body ?: remoteMessage.data["body"] ?: "You have a new notification"

        Log.d(TAG, "Showing notification: $title - $body")
        showNotification(title, body, remoteMessage.data)

        // Handle data payload for app logic
        if (remoteMessage.data.isNotEmpty()) {
            handleDataMessage(remoteMessage.data)
        }
    }

    override fun onNewToken(token: String) {
        super.onNewToken(token)
        Log.d(TAG, "Refreshed token: $token")
        
        // Send token to your server
        sendTokenToServer(token)
    }

    private fun createNotificationChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val importance = NotificationManager.IMPORTANCE_HIGH
            val channel = NotificationChannel(CHANNEL_ID, CHANNEL_NAME, importance).apply {
                description = CHANNEL_DESCRIPTION
                enableLights(true)
                enableVibration(true)
                setShowBadge(true)
                lockscreenVisibility = android.app.Notification.VISIBILITY_PUBLIC
            }

            val notificationManager: NotificationManager =
                getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
            notificationManager.createNotificationChannel(channel)

            Log.d(TAG, "Notification channel created with HIGH importance")
        }
    }

    private fun showNotification(title: String?, body: String?, data: Map<String, String>) {
        try {
            val intent = Intent(this, MainActivity::class.java).apply {
                addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP or Intent.FLAG_ACTIVITY_NEW_TASK)
                // Add notification data to intent
                data.forEach { (key, value) ->
                    putExtra(key, value)
                }
                putExtra("from_notification", true)
            }

            val pendingIntent = PendingIntent.getActivity(
                this,
                System.currentTimeMillis().toInt(),
                intent,
                PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
            )

            // Try to use app icon if notification icon doesn't exist
            val iconResource = try {
                R.drawable.ic_notification
            } catch (e: Exception) {
                Log.w(TAG, "ic_notification not found, using app icon")
                R.mipmap.ic_launcher
            }

            val notificationBuilder = NotificationCompat.Builder(this, CHANNEL_ID)
                .setSmallIcon(iconResource)
                .setContentTitle(title ?: "UEST")
                .setContentText(body ?: "You have a new notification")
                .setAutoCancel(true)
                .setContentIntent(pendingIntent)
                .setPriority(NotificationCompat.PRIORITY_HIGH)
                .setDefaults(NotificationCompat.DEFAULT_ALL)
                .setVisibility(NotificationCompat.VISIBILITY_PUBLIC)
                .setCategory(NotificationCompat.CATEGORY_MESSAGE)

            val notificationManager = getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
            val notificationId = System.currentTimeMillis().toInt()

            notificationManager.notify(notificationId, notificationBuilder.build())

            Log.d(TAG, "✅ Notification displayed successfully: $title - $body (ID: $notificationId)")
        } catch (e: Exception) {
            Log.e(TAG, "❌ Error showing notification: ${e.message}", e)
        }
    }

    private fun handleDataMessage(data: Map<String, String>) {
        Log.d(TAG, "📱 Handling data message: $data")

        // If this is a data-only message (no notification payload), create notification
        val hasNotificationPayload = data.containsKey("title") || data.containsKey("body")

        if (hasNotificationPayload) {
            val title = data["title"] ?: "UEST"
            val body = data["body"] ?: "You have a new notification"
            Log.d(TAG, "📨 Creating notification from data payload: $title - $body")
            showNotification(title, body, data)
        }

        // Handle different types of data messages for app logic
        when (data["type"]) {
            "STUDENT_ACCOUNT_CREATED" -> {
                Log.d(TAG, "🎉 Handling student account created notification")
            }
            "STUDENT_COIN_PURCHASE" -> {
                Log.d(TAG, "💰 Handling coin purchase notification")
            }
            "STUDENT_UWHIZ_PARTICIPATION" -> {
                Log.d(TAG, "📚 Handling exam participation notification")
            }
            else -> {
                Log.d(TAG, "📋 Handling generic notification")
            }
        }
    }

    private fun sendTokenToServer(token: String) {
        // This will be handled by the React Native side
        // The token will be sent to your backend server
        Log.d(TAG, "Token should be sent to server: $token")
    }
}
