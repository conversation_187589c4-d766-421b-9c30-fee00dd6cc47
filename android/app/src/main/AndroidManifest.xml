<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
    <uses-permission android:name="android.permission.READ_MEDIA_DOCUMENTS" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" android:maxSdkVersion="32" />
    <uses-permission android:name="android.permission.CAMERA" />
<uses-permission android:name="android.permission.VIBRATE" />
<uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED"/>
<uses-permission android:name="android.permission.POST_NOTIFICATIONS"/>


    <application
      android:name=".MainApplication"
      android:label="@string/app_name"
      android:icon="@mipmap/ic_launcher"
      android:roundIcon="@mipmap/ic_launcher"
      android:allowBackup="false"
      android:theme="@style/AppTheme"
      android:supportsRtl="true">
      <activity
        android:name=".MainActivity"
        android:label="@string/app_name"

        android:configChanges="keyboard|keyboardHidden|orientation|screenLayout|screenSize|smallestScreenSize|uiMode"
        android:launchMode="singleTask"
        android:windowSoftInputMode="adjustResize"
        android:exported="true">
        <intent-filter>
            <action android:name="android.intent.action.MAIN" />
            <category android:name="android.intent.category.LAUNCHER" />
        </intent-filter>
      </activity>

      <!-- Custom Firebase Messaging Service - Temporarily disabled -->
      <!-- <service
        android:name=".UESTFirebaseMessagingService"
        android:exported="false">
        <intent-filter>
          <action android:name="com.google.firebase.MESSAGING_EVENT" />
        </intent-filter>
      </service> -->

      <!-- Firebase Cloud Messaging Receiver -->
      <receiver
        android:name="io.invertase.firebase.messaging.ReactNativeFirebaseMessagingReceiver"
        android:exported="true">
        <intent-filter>
          <action android:name="com.google.firebase.messaging.RECEIVE" />
        </intent-filter>
      </receiver>

      <!-- Default notification icon and color -->
      <meta-data
        android:name="com.google.firebase.messaging.default_notification_icon"
        android:resource="@drawable/ic_notification"
        tools:replace="android:resource" />
      <meta-data
        android:name="com.google.firebase.messaging.default_notification_color"
        android:resource="@color/notification_color"
        tools:replace="android:resource" />
      <meta-data
        android:name="com.google.firebase.messaging.default_notification_channel_id"
        android:value="uest_notifications"
        tools:replace="android:value" />

    </application>
</manifest>
