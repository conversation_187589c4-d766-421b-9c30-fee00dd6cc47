declare module 'react-native-razorpay' {
  interface RazorpayPrefill {
    email?: string;
    contact?: string;
    name?: string;
  }

  interface RazorpayTheme {
    color?: string;
  }

  interface RazorpayOptions {
    description: string;
    image?: string;
    currency: string;
    key: string;
    amount: string;
    name: string;
    prefill?: RazorpayPrefill;
    theme?: RazorpayTheme;
    order_id?: string;
    modal?: {
      confirm_close?: boolean;
      external?: boolean;
    };
    notes?: Record<string, string>;
  }

  interface RazorpayCheckoutType {
    open(options: RazorpayOptions): Promise<{razorpay_payment_id: string}>;
    onExternalWalletSelection(callback: (data: any) => void): void;
  }

  const RazorpayCheckout: RazorpayCheckoutType;

  export default RazorpayCheckout;
}
