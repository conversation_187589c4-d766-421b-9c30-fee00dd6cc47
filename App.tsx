import React, {useEffect} from 'react';
import AppNavigator from './App/route/AppNavigator';
import {NavigationContainer} from '@react-navigation/native';
import {GestureHandlerRootView} from 'react-native-gesture-handler';
import {Provider} from 'react-redux';
import {store} from './App/Redux/store';
import SplashScreen from 'react-native-splash-screen';
import FirebaseNotificationService from './App/NotificationHelper/FirebaseNotificationService';
import {AppState} from 'react-native';


function App(): React.JSX.Element {
  useEffect(() => {
    // Hide splash screen
    SplashScreen.hide();

    // Initialize Firebase notifications
    initializeNotifications();

    // Handle app state changes
    const handleAppStateChange = (nextAppState: string) => {
      console.log('📱 App state changed to:', nextAppState);
      if (nextAppState === 'active') {
        // App became active - refresh notification count if needed
        console.log('🔄 App became active - refreshing notifications');
      }
    };

    const subscription = AppState.addEventListener('change', handleAppStateChange);

    // Cleanup on unmount
    return () => {
      subscription?.remove();
      FirebaseNotificationService.cleanup();
    };
  }, []);

  const initializeNotifications = async () => {
    try {
      console.log('🚀 Initializing push notifications...');
      const success = await FirebaseNotificationService.initialize();

      if (success) {
        console.log('✅ Push notifications initialized successfully');

        // Get the current token for debugging
        const token = FirebaseNotificationService.getCurrentToken();
        if (token) {
          console.log('🔑 Current FCM Token available');
        }
      } else {
        console.log('❌ Failed to initialize push notifications');
      }
    } catch (error) {
      console.error('❌ Error initializing notifications:', error);
    }
  };

  return (
    <Provider store={store}>
      <GestureHandlerRootView>
        <NavigationContainer>
          <AppNavigator />
        </NavigationContainer>
      </GestureHandlerRootView>
    </Provider>
  );
}

export default App;
