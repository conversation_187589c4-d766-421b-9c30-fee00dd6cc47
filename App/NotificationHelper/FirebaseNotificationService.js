// Firebase-based notification service for UEST app
import messaging from '@react-native-firebase/messaging';
import { Platform, PermissionsAndroid, Alert } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import FCMTokenService from '../services/fcmTokenService';

class FirebaseNotificationService {
  constructor() {
    this.isInitialized = false;
    this.fcmToken = null;
    this.onTokenRefreshListener = null;
    this.onMessageListener = null;
    this.onNotificationOpenedAppListener = null;
  }

  // Initialize Firebase messaging
  async initialize() {
    try {
      console.log('🔥 Initializing Firebase Notification Service...');
      
      // Request permission first
      const hasPermission = await this.requestPermission();
      if (!hasPermission) {
        console.log('❌ Notification permission denied');
        return false;
      }

      // Get FCM token
      await this.getFCMToken();

      // Set up listeners
      this.setupListeners();

      // Handle notification that opened the app from quit state
      await this.handleInitialNotification();

      this.isInitialized = true;
      console.log('✅ Firebase Notification Service initialized successfully');
      return true;
    } catch (error) {
      console.error('❌ Error initializing Firebase Notification Service:', error);
      return false;
    }
  }

  // Request notification permissions
  async requestPermission() {
    try {
      console.log('📱 Requesting notification permissions...');
      
      if (Platform.OS === 'android' && Platform.Version >= 33) {
        const granted = await PermissionsAndroid.request(
          PermissionsAndroid.PERMISSIONS.POST_NOTIFICATIONS,
          {
            title: 'UEST Notification Permission',
            message: 'Allow UEST to send you important notifications about your account, exams, and updates?',
            buttonNeutral: 'Ask Me Later',
            buttonNegative: 'Cancel',
            buttonPositive: 'Allow',
          }
        );
        
        if (granted !== PermissionsAndroid.RESULTS.GRANTED) {
          console.log('❌ Android notification permission denied');
          return false;
        }
      }

      // Request Firebase messaging permission
      const authStatus = await messaging().requestPermission();
      const enabled =
        authStatus === messaging.AuthorizationStatus.AUTHORIZED ||
        authStatus === messaging.AuthorizationStatus.PROVISIONAL;

      if (enabled) {
        console.log('✅ Notification permission granted:', authStatus);
        return true;
      } else {
        console.log('❌ Notification permission denied:', authStatus);
        return false;
      }
    } catch (error) {
      console.error('❌ Error requesting notification permission:', error);
      return false;
    }
  }

  // Get FCM token
  async getFCMToken() {
    try {
      const token = await FCMTokenService.getCurrentToken();
      if (token) {
        this.fcmToken = token;
        console.log('🔑 FCM Token obtained via TokenService');

        // Sync token with server
        await FCMTokenService.syncTokenWithServer(token);

        return token;
      } else {
        console.log('❌ Failed to get FCM token');
        return null;
      }
    } catch (error) {
      console.error('❌ Error getting FCM token:', error);
      return null;
    }
  }

  // Set up notification listeners
  setupListeners() {
    console.log('👂 Setting up notification listeners...');

    // Listen for token refresh
    this.onTokenRefreshListener = messaging().onTokenRefresh(async (token) => {
      console.log('🔄 FCM Token refreshed via Firebase');
      this.fcmToken = token;
      await FCMTokenService.syncTokenWithServer(token);
    });

    // Listen for foreground messages
    this.onMessageListener = messaging().onMessage(async (remoteMessage) => {
      console.log('📨 Foreground notification received:', remoteMessage);
      this.handleForegroundNotification(remoteMessage);
    });

    // Listen for notification opened app
    this.onNotificationOpenedAppListener = messaging().onNotificationOpenedApp((remoteMessage) => {
      console.log('🔔 Notification opened app:', remoteMessage);
      this.handleNotificationNavigation(remoteMessage);
    });
  }

  // Handle initial notification (app opened from quit state)
  async handleInitialNotification() {
    const remoteMessage = await messaging().getInitialNotification();
    if (remoteMessage) {
      console.log('🚀 App opened from notification (quit state):', remoteMessage);
      this.handleNotificationNavigation(remoteMessage);
    }
  }

  // Handle foreground notifications
  handleForegroundNotification(remoteMessage) {
    const { notification, data } = remoteMessage;
    
    // Show local notification or update UI
    Alert.alert(
      notification?.title || 'UEST',
      notification?.body || 'You have a new notification',
      [
        { text: 'Dismiss', style: 'cancel' },
        { 
          text: 'View', 
          onPress: () => this.handleNotificationNavigation(remoteMessage) 
        }
      ]
    );
  }

  // Handle notification navigation
  handleNotificationNavigation(remoteMessage) {
    const { data } = remoteMessage;
    
    console.log('🧭 Handling notification navigation:', data);
    
    // Handle different notification types
    switch (data?.type) {
      case 'STUDENT_ACCOUNT_CREATED':
        // Navigate to profile screen
        console.log('Navigate to profile screen');
        break;
      case 'STUDENT_COIN_PURCHASE':
        // Navigate to wallet screen
        console.log('Navigate to wallet screen');
        break;
      case 'STUDENT_UWHIZ_PARTICIPATION':
        // Navigate to exam results
        console.log('Navigate to exam results');
        break;
      default:
        // Navigate to notifications screen
        console.log('Navigate to notifications screen');
        break;
    }
  }

  // Get stored FCM token
  async getStoredToken() {
    try {
      const tokenData = await FCMTokenService.getStoredTokenData();
      return tokenData ? tokenData.token : null;
    } catch (error) {
      console.error('❌ Error getting stored token:', error);
      return null;
    }
  }

  // Sync token after user login
  async syncTokenAfterLogin() {
    return await FCMTokenService.syncTokenAfterLogin();
  }

  // Remove token on logout
  async removeTokenOnLogout() {
    return await FCMTokenService.removeTokenFromServer();
  }

  // Clean up listeners
  cleanup() {
    console.log('🧹 Cleaning up notification listeners...');
    
    if (this.onTokenRefreshListener) {
      this.onTokenRefreshListener();
    }
    if (this.onMessageListener) {
      this.onMessageListener();
    }
    if (this.onNotificationOpenedAppListener) {
      this.onNotificationOpenedAppListener();
    }
    
    this.isInitialized = false;
  }

  // Check if service is initialized
  isServiceInitialized() {
    return this.isInitialized;
  }

  // Get current FCM token
  getCurrentToken() {
    return this.fcmToken;
  }
}

// Export singleton instance
export default new FirebaseNotificationService();
