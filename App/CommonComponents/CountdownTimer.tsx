import React, {useState, useEffect, memo} from 'react';
import {View, Text, StyleSheet} from 'react-native';
import {differenceInSeconds, addMinutes} from 'date-fns';
import {toZonedTime} from 'date-fns-tz';
import Ionicons from 'react-native-vector-icons/Ionicons';
import {PrimaryColors} from '../Utils/Constants';
import IndexStyle from '../Theme/IndexStyle';

interface CountdownTimerProps {
  exam: any;
}

const CountdownTimer = ({exam}: CountdownTimerProps) => {
  const {styles, isDarkMode} = IndexStyle();

  const [timerState, setTimerState] = useState<
    'registration' | 'application' | 'exam' | 'expired' | 'late'
  >('registration');

  const [countdown, setCountdown] = useState({
    days: 0,
    hours: 0,
    minutes: 0,
    seconds: 0,
  });

  const [examTimer, setExamTimer] = useState({
    minutes: 10,
    seconds: 0,
    isLate: false,
  });

  useEffect(() => {
    const calculateStatus = () => {
      const startRegistrationDate = exam.start_registration_date
        ? new Date(exam.start_registration_date)
        : null;
      const startDate = new Date(exam.start_date);

      if (isNaN(startDate.getTime())) {
        setTimerState('expired');
        setCountdown({days: 0, hours: 0, minutes: 0, seconds: 0});
        setExamTimer({minutes: 0, seconds: 0, isLate: true});
        return;
      }

      const now = toZonedTime(new Date(), 'Asia/Kolkata');
      const istStartDate = toZonedTime(startDate, 'Asia/Kolkata');
      const istStartTime = istStartDate.getTime();
      const startWindowEnd = addMinutes(istStartDate, exam.duration).getTime();
      const nowTime = now.getTime();

      if (
        startRegistrationDate &&
        !isNaN(startRegistrationDate.getTime()) &&
        nowTime < startRegistrationDate.getTime()
      ) {
        const diffSeconds = differenceInSeconds(startRegistrationDate, now);
        const days = Math.floor(diffSeconds / (60 * 60 * 24));
        const hours = Math.floor((diffSeconds % (60 * 60 * 24)) / (60 * 60));
        const minutes = Math.floor((diffSeconds % (60 * 60)) / 60);
        const seconds = diffSeconds % 60;
        setTimerState('registration');
        setCountdown({days, hours, minutes, seconds});
        setExamTimer({minutes: 10, seconds: 0, isLate: false});
      } else if (nowTime < istStartTime) {
        const diffSeconds = differenceInSeconds(istStartDate, now);
        const days = Math.floor(diffSeconds / (60 * 60 * 24));
        const hours = Math.floor((diffSeconds % (60 * 60 * 24)) / (60 * 60));
        const minutes = Math.floor((diffSeconds % (60 * 60)) / 60);
        const seconds = diffSeconds % 60;
        setTimerState('application');
        setCountdown({days, hours, minutes, seconds});
        setExamTimer({minutes: 10, seconds: 0, isLate: false});
      } else if (nowTime >= istStartTime && nowTime <= startWindowEnd) {
        const diffSeconds = differenceInSeconds(new Date(startWindowEnd), now);
        const minutes = Math.floor(diffSeconds / 60);
        const seconds = diffSeconds % 60;
        setTimerState('exam');
        setCountdown({days: 0, hours: 0, minutes: 0, seconds: 0});
        setExamTimer({minutes, seconds, isLate: false});
      } else {
        setTimerState('late');
        setCountdown({days: 0, hours: 0, minutes: 0, seconds: 0});
        setExamTimer({minutes: 0, seconds: 0, isLate: true});
      }
    };

    calculateStatus();
    const interval = setInterval(calculateStatus, 1000);
    return () => clearInterval(interval);
  }, [exam.start_date, exam.start_registration_date, exam.id]);

  const renderTimerMessage = () => {
    switch (timerState) {
      case 'registration':
        return `Registration Starts in: ${countdown.days}d ${countdown.hours}h ${countdown.minutes}m ${countdown.seconds}s`;
      case 'application':
        return ` Exam Starts In :${countdown.days}d ${countdown.hours}h ${countdown.minutes}m ${countdown.seconds}s`;
      case 'exam':
        return `Exam Window: ${examTimer.minutes}m ${examTimer.seconds}s`;
      case 'late':
        return 'You Are Late';
      default:
        return 'Expired';
    }
  };

  return (
    <View
      style={{
        padding: 8,
        alignItems: 'center',
        justifyContent: 'center',
        flexDirection: 'row',
      }}>
      <Ionicons
        name="alarm-outline"
        color="#917D7D"
        size={18}
        style={{marginLeft: 8}}>
        {' '}
      </Ionicons>
      <Text style={{color: '#917D7D', fontSize: 14, fontWeight: 'bold'}}>
         {renderTimerMessage()}
      </Text>
    </View>
  );
};

export default memo(CountdownTimer);
