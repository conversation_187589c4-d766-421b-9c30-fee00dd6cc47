// App/CommonComponents/CommonMessageCard.tsx
import React from 'react';
import {View, Text, StyleSheet} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';
import {PrimaryColors} from '../Utils/Constants';

interface CommonMessageCardProps {
  iconColor?: string;
  borderColor?: string;
  text: string;
  icon?: string;
  textColor?: string;
  backgroundColor?: string;
}

const CommonMessageCard: React.FC<CommonMessageCardProps> = ({
  icon,
  iconColor = PrimaryColors.WARNINGICON,
  backgroundColor,
  borderColor = '#FFA500',
  text,
  textColor,
}) => {
  return (
    <View style={{width:'100%',justifyContent:'center',alignItems:'center',paddingTop:'2%'}}>
    <View
      style={[
        styles.container,
        {backgroundColor, borderLeftColor: borderColor},
      ]}>
      <Icon name={icon} size={20} color={iconColor} style={styles.icon} />
      <Text style={[styles.text, {color: textColor}]}>{text}</Text>
    </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    borderLeftWidth: 5,
    padding: 8,
    borderRadius: 10,
    flexDirection: 'row',
    alignItems: 'center',

    width:'70%',
    
  },
  icon: {
    marginRight: 8,
  },
  text: {
    fontWeight: '600',
    flex: 1,
    fontSize: 14,
  },
});

export default CommonMessageCard;
