/* eslint-disable react-native/no-inline-styles */
import {StyleSheet, Text, View, ScrollView} from 'react-native';
import React, {useRef, useEffect} from 'react';
import {RichEditor, RichToolbar, actions} from 'react-native-pell-rich-editor';
import IndexStyle from '../Theme/IndexStyle';

interface CommonTextEditorProps {
  value: string;
  onChange: (text: string) => void;
  placeholder?: string;
  label: string;
}

const CommonTextEditor: React.FC<CommonTextEditorProps> = ({
  value,
  onChange,
  placeholder,
  label,
}) => {
  const richTextRef = useRef<RichEditor>(null);
  const {styles, isDarkMode} = IndexStyle();

  useEffect(() => {
    if (richTextRef.current) {
      richTextRef.current.setContentHTML(value || '');
    }
  });

  return (
    <View style={BaseStyle.container}>
      <Text style={styles.label}>{label}</Text>
      <ScrollView contentContainerStyle={BaseStyle.scrollContainer}>
        <RichEditor
          androidLayerType="software"
          ref={richTextRef}
          initialContentHTML={value || ''}
          onChange={onChange}
          placeholder={placeholder || 'Write here...'}
          style={BaseStyle.editor}
          editorStyle={{
            backgroundColor: isDarkMode ? '#333' : 'white',
            color: isDarkMode ? '#fff' : '#000',
            placeholderColor: '#a0a0a0',
            contentCSSText: 'font-family: sans-serif; font-size: 14px;',
          }}
        />
      </ScrollView>
      <RichToolbar
        editor={richTextRef}
        actions={[
          actions.setBold,
          actions.setItalic,
          actions.insertBulletsList,
          actions.insertOrderedList,
          actions.insertLink,
        ]}
        style={BaseStyle.toolbar}
      />
    </View>
  );
};

export default CommonTextEditor;

const BaseStyle = StyleSheet.create({
  container: {
    flex: 1,
    marginBottom: 20,
  },
  scrollContainer: {
    flexGrow: 1,
  },
  editor: {
    borderWidth: 1,
    borderColor: '#ccc',
    borderRadius: 5,
    minHeight: 150,
    padding: 10,
    marginBottom: 5,
  },
  toolbar: {
    backgroundColor: '#f5f5f5',
    borderRadius: 5,
  },
});
