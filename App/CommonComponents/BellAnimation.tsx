import React, {useEffect, useRef} from 'react';
import {View, Animated, Easing, StyleSheet} from 'react-native';
import Icon from 'react-native-vector-icons/Ionicons';

const BellAnimation = () => {
  const leftBellAnim = useRef(new Animated.Value(0)).current;
  const rightBellAnim = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    const createShake = (anim: Animated.Value) =>
      Animated.loop(
        Animated.sequence([
          Animated.timing(anim, {
            toValue: -5,
            duration: 100,
            useNativeDriver: true,
            easing: Easing.linear,
          }),
          Animated.timing(anim, {
            toValue: 5,
            duration: 100,
            useNativeDriver: true,
            easing: Easing.linear,
          }),
          Animated.timing(anim, {
            toValue: 0,
            duration: 100,
            useNativeDriver: true,
            easing: Easing.linear,
          }),
        ]),
      );

    createShake(leftBellAnim).start();
    createShake(rightBellAnim).start();
  }, [leftBellAnim, rightBellAnim]);

  return (
    <View style={styles.container}>
      <View style={styles.clockContainer}>
        <Animated.View
          style={[
            styles.bell,
            styles.leftBell,
            {transform: [{translateX: leftBellAnim}]},
          ]}
        />
        <Animated.View
          style={[
            styles.bell,
            styles.rightBell,
            {transform: [{translateX: rightBellAnim}]},
          ]}
        />
        <Icon name="alarm-outline" size={100} color="#ff9900" />
      </View>
    </View>
  );
};

export default BellAnimation;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  clockContainer: {
    width: 100,
    height: 100,
    alignItems: 'center',
    justifyContent: 'center',
  },
  bell: {
    position: 'absolute',
    top: -10,
    width: 14,
    height: 14,
    borderRadius: 7,
    backgroundColor: '#ff9900',
  },
  leftBell: {
    left: 10,
  },
  rightBell: {
    right: 10,
  },
});
