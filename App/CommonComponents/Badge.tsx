import React from 'react';
import { View, Text, StyleSheet } from 'react-native';

interface badge {
    text? : string,
    backgroundColor? : string,
    textColor? : string,
    size? : string,
    style? : {},
}

const Badge = ({ 
  text, 
  backgroundColor = '#ff6347', 
  textColor = '#fff', 
  size = 'small', 
  style 
} : badge) => {
  const sizeStyles = getSizeStyles(size);

  return (
    <View style={[styles.badge, { backgroundColor }, sizeStyles, style]}>
      <Text style={[styles.text, { color: textColor }]}>
        {text}
      </Text>
    </View>
  );
};

// Function to get size styles based on props
const getSizeStyles = (size : string) => {
  switch (size) {
    case 'large':
      return {
        paddingVertical: 10,
        paddingHorizontal: 20,
        borderRadius: 15,
      };
    case 'medium':
      return {
        paddingVertical: 6,
        paddingHorizontal: 15,
        borderRadius: 12,
      };
    case 'small':
    default:
      return {
        paddingVertical: 4,
        paddingHorizontal: 10,
        borderRadius: 10,
      };
  }
};

const styles = StyleSheet.create({
  badge: {
    alignSelf: 'flex-start',
    backgroundColor: '#ff6347',
    marginTop : 7
  },
  text: {
    fontSize: 12,
    fontWeight: 'bold',
  },
});

export default Badge;
