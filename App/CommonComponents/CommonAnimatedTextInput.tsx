import React, { useRef, useState, useEffect } from 'react';
import { Animated, TextInput, View, StyleSheet, TextInputProps, Pressable } from 'react-native';
import Ionicons from 'react-native-vector-icons/Ionicons';

interface CommonAnimatedTextInputProps extends TextInputProps {
  label: string;
  value: string;
  onChangeText: (text: string) => void;
  keyboardType?: TextInputProps['keyboardType'];
  icon?: string; // Ionicons icon name
  iconColor?: string;
  onPressIn?: () => void;
  onPressOut?: () => void;
}

const CommonAnimatedTextInput: React.FC<CommonAnimatedTextInputProps> = ({
  label,
  value,
  onChangeText,
  keyboardType = 'default',
  icon,
  iconColor = '#FD904B',
  onPressIn,
  onPressOut,
  ...rest
}) => {
  const [isFocused, setIsFocused] = useState(false);
  const animatedLabel = useRef(new Animated.Value(value ? 1 : 0)).current;

  useEffect(() => {
    Animated.timing(animatedLabel, {
      toValue: isFocused || value ? 1 : 0,
      duration: 220,
      useNativeDriver: false,
    }).start();
  }, [isFocused, value]);

  const labelStyle = {
    position: 'absolute' as const,
    left: icon ? 44 : 18,
    top: animatedLabel.interpolate({
      inputRange: [0, 1],
      outputRange: [16, -10],
    }),
    fontSize: animatedLabel.interpolate({
      inputRange: [0, 1],
      outputRange: [16, 12],
    }),
    color: animatedLabel.interpolate({
      inputRange: [0, 1],
      outputRange: ['#888', iconColor],
    }),
    backgroundColor: '#fff',
    paddingHorizontal: 4,
    zIndex: 2,
    fontWeight: '500',
  };

  return (
    <Pressable
      style={styles.container}
      onPressIn={onPressIn}
      onPressOut={onPressOut}
    >
      {icon && (
        <View style={styles.iconContainer}>
          <Ionicons name={icon} size={22} color={iconColor} />
        </View>
      )}
      <Animated.Text style={labelStyle} pointerEvents="none">
        {label}
      </Animated.Text>
      <TextInput
        style={[styles.input, {paddingLeft: icon ? 44 : 16}]}
        value={value}
        onChangeText={onChangeText}
        onFocus={() => setIsFocused(true)}
        onBlur={() => setIsFocused(false)}
        keyboardType={keyboardType}
        {...rest}
      />
    </Pressable>
  );
};

const styles = StyleSheet.create({
  container: {
    marginVertical: 14,
    width: '85%',
    alignSelf: 'center',
    justifyContent: 'center',
  },
  iconContainer: {
    position: 'absolute',
    left: 14,
    top: 13,
    zIndex: 3,
  },
  input: {
    height: 52,
    borderWidth: 1.2,
    borderColor: '#222',
    borderRadius: 12,
    paddingHorizontal: 16,
    fontSize: 16,
    backgroundColor: '#fff',
    color: '#000',
    zIndex: 1,
    fontWeight: '500',
  },
});

export default CommonAnimatedTextInput; 