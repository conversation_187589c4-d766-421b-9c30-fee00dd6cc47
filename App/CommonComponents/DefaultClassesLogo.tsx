/* eslint-disable react-native/no-inline-styles */
// components/Header.js
import React from 'react';
import {Text} from 'react-native';
import {PrimaryColors} from '../Utils/Constants';
import LinearGradient from 'react-native-linear-gradient';

const DefaultClassesLogo = ({
  firstName,
  lastName,
}: {
  firstName: string;
  lastName: string;
  style?: any;
}) => {
  return (
    <LinearGradient
      colors={[PrimaryColors.ORANGE, PrimaryColors.BLACK]}
      style={{flex: 1, alignItems: 'center', justifyContent: 'center'}}
      start={{x: 0, y: 0}}
      end={{x: 1, y: 1}}>
      <Text
        style={{fontSize: 50, fontWeight: 'bold', color: PrimaryColors.WHITE}}>
        {firstName?.[0] || ''}
        {lastName?.[0] || ''}
      </Text>
    </LinearGradient>
  );
};

export default DefaultClassesLogo;
