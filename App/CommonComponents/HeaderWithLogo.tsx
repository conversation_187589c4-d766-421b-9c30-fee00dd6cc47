import {StyleSheet, View, Image} from 'react-native';
import React from 'react';
import {IMAGE_CONSTANT, PrimaryColors} from '../Utils/Constants';

const HeaderWithLogo = () => {
  return (
    <View style={styles.headerContainer}>
      <Image source={IMAGE_CONSTANT.COMPANYLOGO} style={styles.logo} />
    </View>
  );
};

export default HeaderWithLogo;

const styles = StyleSheet.create({
  headerContainer: {
    height: 180,
    width: '100%',
    backgroundColor: PrimaryColors.BLACK,
    borderBottomLeftRadius: 40,
    borderBottomRightRadius: 40,
    overflow: 'hidden',
    justifyContent: 'center',
    flexDirection: 'row',
    paddingTop: '12%',
    marginBottom: 50,
  },
  logo: {
    width: '70%',
    height: 100,
  },
});
