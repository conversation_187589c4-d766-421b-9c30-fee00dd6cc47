// components/Header.js
import React from 'react';
import {StyleSheet, Text, View} from 'react-native';
import {PrimaryColors} from '../Utils/Constants';

const Header = ({title}: {title: string}) => {
  return (
    <View style={styles.headerContainer}>
      <Text style={styles.title}>{title}</Text>
    </View>
  );
};

export default Header;

const styles = StyleSheet.create({
  headerContainer: {
    height: 110,
    width: '100%',
    backgroundColor: PrimaryColors.BLACK,
    borderBottomLeftRadius: 40,
    borderBottomRightRadius: 40,
    overflow: 'hidden',
    justifyContent: 'center',
    flexDirection: 'row',
    paddingTop: '12%',
  },
  title: {
    color: PrimaryColors.WHITE,
    fontSize: 22,
    fontWeight: 'bold',
  },
});
