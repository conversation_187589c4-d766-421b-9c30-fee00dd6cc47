/* eslint-disable react-native/no-inline-styles */
import React, {useState} from 'react';
import {Text, View, Platform, TouchableOpacity} from 'react-native';
import DateTimePicker, {
  DateTimePickerEvent,
} from '@react-native-community/datetimepicker';
import {PrimaryColors} from '../Utils/Constants';
import IndexStyle from '../Theme/IndexStyle';
import Icon from 'react-native-vector-icons/MaterialIcons'; // Import the icon library


interface CommonComponentsProps {
  label?: string;
  value: Date | null;
  innerText: string;
  mode?: 'date' | 'time' | 'datetime';
  onChange: (selectedDate: Date) => void;
  minimumDate?: Date;
  maximumDate?: Date;
  style?: any;
}

const formatDate = (date: Date) => {
  if (!date) {
    return '';
  }
  return date
    .toLocaleDateString('en-GB', {
      day: '2-digit',
      month: 'short',
      year: 'numeric',
    })
    .replace(',', ',');
};

const CommonDateTimePicker: React.FC<CommonComponentsProps> = ({
  label,
  value,
  innerText,
  mode = 'date',
  onChange,
  minimumDate,
  maximumDate,
  style,
}) => {
  const [showPicker, setShowPicker] = useState(false);
  const {styles, isDarkMode} = IndexStyle();

  const handleChange = (event: DateTimePickerEvent, selectedDate?: Date) => {
    setShowPicker(Platform.OS === 'ios');
    if (selectedDate) {
      onChange(selectedDate);
    }
  };

  return (
    <View style={[{width: '80%', marginBottom: 20,}, style]}>
      <Text style={styles.label1}>{label}</Text>
      <TouchableOpacity
        onPress={() => setShowPicker(true)}
        activeOpacity={0.7}
        style={[
          styles.input,
          {
            borderWidth: 1,
            justifyContent: 'space-between',
            backgroundColor: isDarkMode ? '#161616' : '#FFFFFF',
          borderColor: isDarkMode ?'#CCCCCC' : '#CCCCCC',flexDirection:'row',
          },
        ]}>
        <Text
          style={{
            color: value
              ? isDarkMode
                ? PrimaryColors.WHITE
                : PrimaryColors.BLACK
              : PrimaryColors.GRAYSHADOW,
          }}>
          {value ? formatDate(value) : innerText}
        </Text>
        <Icon
          name="calendar-month" // Use a calendar icon name from MaterialIcons
          size={20}
          color={isDarkMode ? '#FFFFFF' : '#000000'}
          style={{ marginRight: 10 }}
        />
      </TouchableOpacity>
      {showPicker && (
        <DateTimePicker
          value={value || new Date()}
          mode={mode}
          display={Platform.OS === 'ios' ? 'spinner' : 'default'}
          onChange={handleChange}
          minimumDate={minimumDate}
          maximumDate={maximumDate}
        />
      )}
    </View>
  );
};

export default CommonDateTimePicker;
