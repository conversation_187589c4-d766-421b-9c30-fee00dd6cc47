import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import { PrimaryColors } from '../Utils/Constants';

interface Props {
  value: number;
  max: number;
  height?: number;
  color?: string;
  backgroundColor?: string;
}

const LinearProgressBar: React.FC<Props> = ({
  value,
  max,
  height = 20,
  backgroundColor = '#e0e0e0',
}) => {
  const percentage = Math.min((value / max) * 100, 100);

  return (
    <View style={[styles.container, { height, backgroundColor }]}>
      <View style={[styles.gradientWrapper, { width: `${percentage}%` }]}>
        <LinearGradient
          colors={['#ffffff', PrimaryColors.ORANGEFORTOGGLE]}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 0 }}
          style={[styles.gradient, { height }]}
        />
      </View>
      <Text style={styles.label}>{`${value} / ${max}`}</Text>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    width: '100%',
    borderRadius: 10,
    overflow: 'hidden',
    justifyContent: 'center',
    alignItems: 'center',
    position: 'relative',
  },
  gradientWrapper: {
    position: 'absolute',
    left: 0,
    top: 0,
    bottom: 0,
  },
  gradient: {
    width: '100%',
  },
  label: {
    zIndex: 1,
    color: '#000',
    fontWeight: 'bold',
  },
});

export default LinearProgressBar;
