/* eslint-disable react-native/no-inline-styles */
import {
  Text,
  View,
  Platform,
  PermissionsAndroid,
  Alert,
  TouchableOpacity,
} from 'react-native';
import React from 'react';
import {
  pick,
  types,
  errorCodes,
  DocumentPickerResponse,
} from '@react-native-documents/picker';
import IndexStyle from '../Theme/IndexStyle';
import {PrimaryColors} from '../Utils/Constants';

interface CommonFileUploadProps {
  label: string;
  fileName: string | null;
  placeholder?: string;
  onFileSelect: (file: DocumentPickerResponse) => void;
  style?: any;
}

const CommonFileUpload: React.FC<CommonFileUploadProps> = ({
  label,
  fileName,
  placeholder,
  onFileSelect,
  style,
}) => {
  const {styles, isDarkMode} = IndexStyle();

  const requestStoragePermission = async () => {
    if (Platform.OS === 'android') {
      try {
        if (Platform.Version >= 33) {
          const granted = await PermissionsAndroid.request(
            PermissionsAndroid.PERMISSIONS.READ_MEDIA_IMAGES,
            {
              title: 'Storage Permission',
              message: 'App needs access to your files to upload documents.',
              buttonNeutral: 'Ask Me Later',
              buttonNegative: 'Cancel',
              buttonPositive: 'OK',
            },
          );
          return granted === PermissionsAndroid.RESULTS.GRANTED;
        } else {
          const granted = await PermissionsAndroid.request(
            PermissionsAndroid.PERMISSIONS.READ_EXTERNAL_STORAGE,
            {
              title: 'Storage Permission',
              message: 'App needs access to your files to upload documents.',
              buttonNeutral: 'Ask Me Later',
              buttonNegative: 'Cancel',
              buttonPositive: 'OK',
            },
          );
          return granted === PermissionsAndroid.RESULTS.GRANTED;
        }
      } catch (error) {
        console.log('Permission error:', error);
        return false;
      }
    } else {
      return true;
    }
  };

  const handleDocumentPick = async () => {
    const hasPermission = await requestStoragePermission();
    if (!hasPermission) {
      Alert.alert(
        'Permission Denied',
        'Please allow file access to pick a document.',
      );
      return;
    }

    try {
      const [res] = await pick({
        type: [types.images, types.pdf],
      });
      console.log(res);
      if (res) {
        onFileSelect(res);
      }
    } catch (err) {
      if (err === errorCodes.OPERATION_CANCELED) {
        console.log('User cancelled the picker');
      } else {
        console.error('Unknown error:', err);
      }
    }
  };

  return (
    <View style={[{width: '80%', marginBottom: 10}, style]}>
      <Text style={styles.label}>{label}</Text>
      <TouchableOpacity style={styles.input} onPress={handleDocumentPick}>
        <Text
          style={{
            color: fileName
              ? isDarkMode
                ? PrimaryColors.WHITE
                : PrimaryColors.BLACK
              : PrimaryColors.GRAYSHADOW,
          }}>
          {fileName ? fileName : placeholder || 'Upload File'}
        </Text>
      </TouchableOpacity>
    </View>
  );
};

export default CommonFileUpload;
