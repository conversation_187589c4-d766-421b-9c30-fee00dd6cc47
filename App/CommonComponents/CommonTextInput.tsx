/* eslint-disable react/self-closing-comp */
/* eslint-disable react-native/no-inline-styles */
import {Text, TextInputProps, View} from 'react-native';
import React from 'react';
import {PrimaryColors} from '../Utils/Constants';
import IndexStyle from '../Theme/IndexStyle';
import {TextInput} from 'react-native-gesture-handler';

interface CommonTextInputProps extends TextInputProps {
  label?: string;
  placeholder: string;
  value: string;
  onChangeText: (text: string) => void;
  secureTextEntry?: boolean;
  multiline?: boolean;
  style?: any;
  keyboardType?: any;
}

const CommonTextInput: React.FC<CommonTextInputProps> = ({
  label,
  placeholder,
  value,
  onChangeText,
  secureTextEntry = false,
  multiline = false,
  style,
  keyboardType,
}) => {
  const {styles, isDarkMode} = IndexStyle();
  return (
    <View style={[{width: '80%', marginBottom: 10}, style]}>
      <Text style={styles.label1}>{label}</Text>
      <TextInput
        style={[
          styles.input,
          {
            borderWidth: 1,
           borderColor: isDarkMode ? PrimaryColors.BLACK : '#CCCCCC',
            color: isDarkMode ? PrimaryColors.WHITE : PrimaryColors.BLACK,
            backgroundColor: isDarkMode ? '#1B1B1B' : '#FFFFFF',
          },
          multiline ? {height: 150, textAlignVertical: 'top'} : {},
          style,
          keyboardType ? {keyboardType: keyboardType} : {},
        ]}
        placeholder={placeholder}
        placeholderTextColor={'#B1B1B1'}
        secureTextEntry={secureTextEntry}
        value={value}
        onChangeText={onChangeText}
        multiline={multiline}
        keyboardType={keyboardType}></TextInput>
    </View>
  );
};

export default CommonTextInput;
