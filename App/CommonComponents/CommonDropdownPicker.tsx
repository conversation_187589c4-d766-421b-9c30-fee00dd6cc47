import React from 'react';
import {View, StyleSheet} from 'react-native';
import {Dropdown} from 'react-native-element-dropdown';
import {PrimaryColors} from '../Utils/Constants';
import IndexStyle from '../Theme/IndexStyle';
import {Text} from 'react-native-gesture-handler';

interface CommonDropdownPickerProps {
  data: Array<{label: string; value: any}>;
  value: any;
  onChange: (item: any) => void;
  placeholder: string;
  style?: any;
  label: string;
  numberOfLines?: number;
  ellipsizeMode?: 'head' | 'middle' | 'tail';
}

const CommonDropdownPicker: React.FC<CommonDropdownPickerProps> = ({
  data,
  value,
  onChange,
  placeholder,
  label,
  style,
  numberOfLines,
  ellipsizeMode,
}) => {
  const {styles, isDarkMode} = IndexStyle();
  return (
    <View style={[style]}>
      <Text
        style={styles.label1}
        numberOfLines={numberOfLines}
        ellipsizeMode={ellipsizeMode}>
        {label}
      </Text>
      <Dropdown
        style={[
          baseStyles.dropdown,
          {
            backgroundColor: isDarkMode ? '#1B1B1B' : '#FFFFFF',
            borderColor: isDarkMode ? PrimaryColors.BLACK : '#CCCCCC',
          },
        ]}
        placeholderStyle={{
          color: PrimaryColors.GRAYSHADOW,
        }}
        selectedTextStyle={{
          color: isDarkMode ? PrimaryColors.WHITE : PrimaryColors.BLACK,
        }}
        itemContainerStyle={{
          backgroundColor: isDarkMode
            ? PrimaryColors.BLACK
            : PrimaryColors.WHITE,
        }}
        itemTextStyle={{
          color: isDarkMode ? PrimaryColors.WHITE : PrimaryColors.BLACK,
        }}
        activeColor={
          isDarkMode ? PrimaryColors.LIGHTGRAY : PrimaryColors.LIGHTGRAY2
        }
        data={data}
        labelField="label"
        valueField="value"
        placeholder={placeholder}
        value={value}
        onChange={item => onChange(item.value)}
      />
    </View>
  );
};

export default CommonDropdownPicker;

const baseStyles = StyleSheet.create({
  dropdown: {
    height: 45,
    borderWidth: 0.5,
    borderRadius: 8,
    paddingHorizontal: 10,
  },
});
