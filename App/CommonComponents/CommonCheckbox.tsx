/* eslint-disable react-native/no-inline-styles */
import {Text, View} from 'react-native';
import React from 'react';
import {PrimaryColors} from '../Utils/Constants';
import CheckBox from 'react-native-check-box';
import IndexStyle from '../Theme/IndexStyle';

interface CommonCheckboxProps {
  isChecked: boolean;
  onClick: () => void;
  label: string;
}

const CommonCheckbox: React.FC<CommonCheckboxProps> = ({
  isChecked,
  onClick,
  label,
}) => {
  const {isDarkMode} = IndexStyle();
  return (
    <View style={{flexDirection: 'row', alignItems: 'center'}}>
      <CheckBox
        onClick={onClick}
        isChecked={isChecked}
        checkBoxColor={isDarkMode ? PrimaryColors.WHITE : PrimaryColors.BLACK}
      />
      <Text
        style={{
          marginLeft: 10,
          color: isDarkMode ? PrimaryColors.WHITE : PrimaryColors.BLACK,
          flexShrink: 1,
        }}>
        {label}
      </Text>
    </View>
  );
};

export default CommonCheckbox;
