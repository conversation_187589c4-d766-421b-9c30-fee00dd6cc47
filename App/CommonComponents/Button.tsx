import {StyleSheet, Text, TouchableOpacity} from 'react-native';
import React from 'react';
import {PrimaryColors} from '../Utils/Constants';

interface ButtonProps {
  title: string;
  onPress: () => void;
  disabled?: boolean;
}

const Button: React.FC<ButtonProps> = ({title, onPress}) => {
  return (
    <TouchableOpacity style={styles.button} onPress={onPress}>
      <Text style={styles.buttonText}>{title}</Text>
    </TouchableOpacity>
  );
};

export default Button;

const styles = StyleSheet.create({
  button: {
    backgroundColor: '#FF914D',
    width: '80%',
    padding: 15,
    borderRadius: 8,
    marginTop: 20,
    alignItems: 'center',
  },
  buttonText: {
    color: PrimaryColors.WHITE,
    fontSize: 18,
    fontWeight: '600',
  },
});
