import {StyleSheet, Switch} from 'react-native';
import React from 'react';
import {PrimaryColors} from '../Utils/Constants';
import IndexStyle from '../Theme/IndexStyle';
interface ButtonProps {
  value: boolean;
  onToggle: (value: boolean) => void;
}

const Toggle: React.FC<ButtonProps> = ({value, onToggle}) => {
  const {isDarkMode} = IndexStyle();

  return (
    <Switch
      trackColor={{false: 'gray', true: PrimaryColors.ORANGEFORTOGGLE}}
      thumbColor={
        isDarkMode ? PrimaryColors.WHITE : PrimaryColors.ORANGEFORTOGGLE
      }
      ios_backgroundColor={PrimaryColors.ORANGEFORTOGGLE}
      onValueChange={onToggle}
      value={value}
      style={BaseStyle.switch}
    />
  );
};

export default Toggle;

const BaseStyle = StyleSheet.create({
  switch: {
    transform: [{scaleX: 1.3}, {scaleY: 1.3}],
    borderWidth: 2,
    borderColor: PrimaryColors.BLACK,
  },
});
