import React from 'react';
import { View, Text, TouchableOpacity, StyleSheet, Platform } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import Ionicons from 'react-native-vector-icons/Ionicons';
import { PrimaryColors } from '../Utils/Constants';
import IndexStyle from '../Theme/IndexStyle';

// Props:
// - title: string (required) - The title to display in the header
// - isBack: boolean (optional, default: false) - Whether to show the back button
// - onBackPress: function (optional) - Custom function to call when back button is pressed
const CurvHeader = ({ title, isBack = false, onBackPress }) => {
  const navigation = useNavigation();
  const {isDarkMode} = IndexStyle();
  const handleBackPress = () => {
    if (isBack) {
      if (onBackPress) {
        onBackPress();
      } else {
        navigation.goBack();
      }
    }
  };

  return (
    <View style={styles.container}>
      {/* Left: Back Button or Placeholder */}
      {isBack ? (
        <View style={styles.backContainer}>
          <TouchableOpacity onPress={handleBackPress} style={styles.backButton}>
            <Ionicons name="arrow-back" size={24} color={PrimaryColors.WHITE} style={{marginTop:12}} />
          </TouchableOpacity>
        </View>
      ) : (
        <View style={styles.emptyBackContainer} />
      )}

      {/* Center: Title */}
      <View style={styles.titleContainer}>
        <Text style={styles.titleText}>{title}</Text>
      </View>

      {/* Right: Placeholder to balance the layout */}
      <View style={styles.rightPlaceholder} />

      {/* Curve Overlay for Upward Curve Effect */}
      <View style={[styles.curveOverlay,{backgroundColor: isDarkMode ? '#161616' : PrimaryColors.WHITE }]} />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    paddingHorizontal: '5%',
    width: '100%',
    backgroundColor: PrimaryColors.BLACK,
    height: Platform.OS === 'android' ? 100 : 120 ,
  },
  backContainer: {
    alignSelf: 'flex-start',
    height: '100%',
    justifyContent: 'center',
  },
  emptyBackContainer: {
    width: 34, // Matches the approximate width of the back button (24 icon + 5 padding on each side)
    height: '100%',
  },
  backButton: {
    padding: 5,
  },
  titleContainer: {
    justifyContent: 'center',
    height: '100%',
    flex: 1,
  },
  titleText: {
    textAlign: 'center',
    fontSize: 24,
    color: PrimaryColors.WHITE,
    marginTop:8
  },
  rightPlaceholder: {
    width: 34,
    height: '100%',
  },
  curveOverlay: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    height: 20,
    // backgroundColor: PrimaryColors.BLACK,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
  },
});

export default CurvHeader;