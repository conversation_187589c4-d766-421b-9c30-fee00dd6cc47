import moment from "moment";

export const getDateCalenderFormat = (date : string) => {
  const m = moment(date);
  return {
    dateString: m.format('YYYY-MM-DD'),
    day: m.date(), 
    month: m.month() + 1,
    timestamp: m.valueOf(),
    year: m.year(),
  };
};

export const getStatusStyle = (status: string) => {
  switch (status) {
    case 'APPROVED':
      return 'green';
    case 'REJECTED':
      return 'red';
    case 'PENDING':
      return  'orange';
    default:
      return 'black';
  }
};

export const dateFormat = (date: Date) => {
  return moment(date).format('DD/MM/YYYY');
};