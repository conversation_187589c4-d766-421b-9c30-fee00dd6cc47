// Android Push Notification Tester for UEST app
import messaging from '@react-native-firebase/messaging';
import { Alert, Platform, PermissionsAndroid } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';

class AndroidNotificationTester {
  
  // Test 1: Check if all permissions are granted
  static async testPermissions() {
    console.log('🧪 Testing Android notification permissions...');
    
    try {
      if (Platform.OS !== 'android') {
        Alert.alert('Error', 'This test is for Android only');
        return false;
      }

      // Check POST_NOTIFICATIONS permission for Android 13+
      if (Platform.Version >= 33) {
        const granted = await PermissionsAndroid.check(
          PermissionsAndroid.PERMISSIONS.POST_NOTIFICATIONS
        );
        
        console.log('📱 POST_NOTIFICATIONS permission:', granted ? 'GRANTED' : 'DENIED');
        
        if (!granted) {
          Alert.alert(
            'Permission Required',
            'POST_NOTIFICATIONS permission is required for push notifications. Please grant it in app settings.',
            [{ text: 'OK' }]
          );
          return false;
        }
      }

      // Check Firebase messaging authorization
      const authStatus = await messaging().requestPermission();
      const enabled =
        authStatus === messaging.AuthorizationStatus.AUTHORIZED ||
        authStatus === messaging.AuthorizationStatus.PROVISIONAL;

      console.log('🔥 Firebase messaging permission:', enabled ? 'GRANTED' : 'DENIED');

      Alert.alert(
        'Permission Test Result',
        `Android Version: ${Platform.Version}\nPOST_NOTIFICATIONS: ${Platform.Version >= 33 ? 'GRANTED' : 'NOT_REQUIRED'}\nFirebase Messaging: ${enabled ? 'GRANTED' : 'DENIED'}`,
        [{ text: 'OK' }]
      );

      return enabled;
    } catch (error) {
      console.error('❌ Permission test failed:', error);
      Alert.alert('Permission Test Failed', error.message);
      return false;
    }
  }

  // Test 2: Get and display FCM token
  static async testFCMToken() {
    console.log('🧪 Testing FCM token generation...');
    
    try {
      const token = await messaging().getToken();
      
      if (token) {
        console.log('🔑 FCM Token generated successfully');
        console.log('Token:', token);
        
        // Store token for testing
        await AsyncStorage.setItem('test_fcm_token', token);
        
        Alert.alert(
          'FCM Token Test',
          `Token Generated: ✅\nLength: ${token.length}\n\nToken logged to console for testing.`,
          [
            { text: 'Copy Token', onPress: () => this.logTokenForCopy(token) },
            { text: 'OK' }
          ]
        );
        
        return token;
      } else {
        console.log('❌ Failed to generate FCM token');
        Alert.alert('FCM Token Test', 'Failed to generate token ❌');
        return null;
      }
    } catch (error) {
      console.error('❌ FCM token test failed:', error);
      Alert.alert('FCM Token Test Failed', error.message);
      return null;
    }
  }

  // Test 3: Test notification display (simulate background notification)
  static async testNotificationDisplay() {
    console.log('🧪 Testing notification display...');
    
    try {
      // This simulates what happens when a notification is received in background
      const testNotification = {
        title: '🧪 Test Notification',
        body: 'This is a test notification from UEST app',
        data: {
          type: 'TEST_NOTIFICATION',
          testId: Date.now().toString()
        }
      };

      console.log('📨 Simulating background notification:', testNotification);
      
      Alert.alert(
        testNotification.title,
        testNotification.body,
        [
          { text: 'Dismiss', style: 'cancel' },
          { text: 'Open App', onPress: () => console.log('App opened from notification') }
        ]
      );

      return true;
    } catch (error) {
      console.error('❌ Notification display test failed:', error);
      Alert.alert('Notification Display Test Failed', error.message);
      return false;
    }
  }

  // Test 4: Send test notification using Firebase Console format
  static async sendTestNotification() {
    console.log('🧪 Preparing test notification...');
    
    try {
      const token = await AsyncStorage.getItem('test_fcm_token');
      
      if (!token) {
        Alert.alert(
          'No Token Found',
          'Please run "Test FCM Token" first to generate a token.',
          [{ text: 'OK' }]
        );
        return false;
      }

      const testPayload = {
        to: token,
        notification: {
          title: "🎉 UEST Test Notification",
          body: "Your push notification setup is working correctly!"
        },
        data: {
          type: "TEST_NOTIFICATION",
          timestamp: new Date().toISOString(),
          testId: Date.now().toString()
        }
      };

      console.log('📤 Test notification payload:');
      console.log(JSON.stringify(testPayload, null, 2));

      Alert.alert(
        'Test Notification Payload Ready',
        'Copy the payload from console and send it using:\n\n1. Firebase Console > Cloud Messaging\n2. Postman to FCM API\n3. Your backend server\n\nPayload logged to console.',
        [
          { text: 'Show Payload', onPress: () => this.showPayloadInAlert(testPayload) },
          { text: 'OK' }
        ]
      );

      return testPayload;
    } catch (error) {
      console.error('❌ Test notification preparation failed:', error);
      Alert.alert('Test Notification Failed', error.message);
      return false;
    }
  }

  // Test 5: Check notification settings
  static async checkNotificationSettings() {
    console.log('🧪 Checking notification settings...');
    
    try {
      const authStatus = await messaging().requestPermission();
      const token = await messaging().getToken();
      
      const settings = {
        authorizationStatus: authStatus,
        hasToken: !!token,
        tokenLength: token ? token.length : 0,
        androidVersion: Platform.Version,
        isAndroid13Plus: Platform.Version >= 33
      };

      console.log('⚙️ Notification settings:', settings);

      Alert.alert(
        'Notification Settings',
        `Authorization: ${authStatus}\nHas Token: ${settings.hasToken ? 'Yes' : 'No'}\nToken Length: ${settings.tokenLength}\nAndroid Version: ${settings.androidVersion}\nRequires POST_NOTIFICATIONS: ${settings.isAndroid13Plus ? 'Yes' : 'No'}`,
        [{ text: 'OK' }]
      );

      return settings;
    } catch (error) {
      console.error('❌ Settings check failed:', error);
      Alert.alert('Settings Check Failed', error.message);
      return null;
    }
  }

  // Run all Android tests
  static async runAllAndroidTests() {
    console.log('🚀 Running all Android notification tests...');
    
    const results = {
      permissions: false,
      token: false,
      display: false,
      settings: false
    };

    try {
      // Test 1: Permissions
      console.log('📱 Test 1: Permissions');
      results.permissions = await this.testPermissions();
      await this.delay(1000);

      // Test 2: FCM Token
      console.log('🔑 Test 2: FCM Token');
      const token = await this.testFCMToken();
      results.token = !!token;
      await this.delay(1000);

      // Test 3: Notification Display
      console.log('📨 Test 3: Notification Display');
      results.display = await this.testNotificationDisplay();
      await this.delay(1000);

      // Test 4: Settings Check
      console.log('⚙️ Test 4: Settings Check');
      const settings = await this.checkNotificationSettings();
      results.settings = !!settings;

      // Show summary
      this.showTestSummary(results);
      
      return results;
    } catch (error) {
      console.error('❌ Test suite failed:', error);
      Alert.alert('Test Suite Failed', error.message);
      return results;
    }
  }

  // Helper methods
  static logTokenForCopy(token) {
    console.log('=== FCM TOKEN FOR TESTING ===');
    console.log(token);
    console.log('=== END TOKEN ===');
    Alert.alert('Token Logged', 'FCM token has been logged to console. You can copy it from there.');
  }

  static showPayloadInAlert(payload) {
    Alert.alert(
      'Test Payload',
      JSON.stringify(payload, null, 2),
      [{ text: 'OK' }]
    );
  }

  static showTestSummary(results) {
    const summary = `
📊 ANDROID NOTIFICATION TEST SUMMARY

✅ Permissions: ${results.permissions ? 'PASS' : 'FAIL'}
✅ FCM Token: ${results.token ? 'PASS' : 'FAIL'}  
✅ Display Test: ${results.display ? 'PASS' : 'FAIL'}
✅ Settings: ${results.settings ? 'PASS' : 'FAIL'}

Platform: Android ${Platform.Version}
    `.trim();

    console.log(summary);
    
    Alert.alert(
      'Android Test Summary',
      summary,
      [{ text: 'OK' }]
    );
  }

  static delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

export default AndroidNotificationTester;
