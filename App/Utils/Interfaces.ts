
export interface CalenderDate {
  dateString: string;
  day: number;
  month: number;
  timestamp: number;
  year: number;
}


export interface TimeTable {
  id : number,
  slot_id: number;
  subject_name: string;
  faculty_name: string;
  days: string;
  timeslot_start_time: string;
  timeslot_end_time: string;
  timeslot_is_break: string;
  timeslot_break_name: string;
  timeslot?: string;
  name : string;
  height?: number;
  resource_name: string;
  notes : string;
  class_name : string;
  department_name : string;
}

export interface TimeTableItem {
  [key: string]: TimeTable[];
}

export interface CalenderProp {
  type: string;
  loadItems: (day: CalenderDate) => void;
  renderItem: (item : any) => any;
  items:  TimeTableItem | WorkItem ;
}

export interface Work {
  id: number;
  faculty_name: string;
  subject_name: string;
  name : string;
  work : string;
  height?: number;
  description:string;
  title : string;
}

export interface Homework extends Work {
  homework: string;
}

export interface Classwork extends Work {
  description: string;
}

export interface WorkItem {
  [key: string]: Work[];
}