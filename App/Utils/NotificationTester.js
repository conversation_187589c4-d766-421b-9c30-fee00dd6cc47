// Notification Testing Utility for UEST app
import messaging from '@react-native-firebase/messaging';
import { Alert, Platform } from 'react-native';
import FirebaseNotificationService from '../NotificationHelper/FirebaseNotificationService';
import FCMTokenService from '../services/fcmTokenService';

class NotificationTester {
  // Test notification permissions
  static async testPermissions() {
    console.log('🧪 Testing notification permissions...');
    
    try {
      const authStatus = await messaging().requestPermission();
      const enabled =
        authStatus === messaging.AuthorizationStatus.AUTHORIZED ||
        authStatus === messaging.AuthorizationStatus.PROVISIONAL;

      const result = {
        status: authStatus,
        enabled,
        platform: Platform.OS,
      };

      console.log('📋 Permission test result:', result);
      
      Alert.alert(
        'Permission Test Result',
        `Status: ${authStatus}\nEnabled: ${enabled}\nPlatform: ${Platform.OS}`,
        [{ text: 'OK' }]
      );

      return result;
    } catch (error) {
      console.error('❌ Permission test failed:', error);
      Alert.alert('Permission Test Failed', error.message);
      return null;
    }
  }

  // Test FCM token generation
  static async testTokenGeneration() {
    console.log('🧪 Testing FCM token generation...');
    
    try {
      const token = await FCMTokenService.getCurrentToken();
      const tokenInfo = await FCMTokenService.getTokenInfo();

      console.log('📋 Token test result:', tokenInfo);
      
      Alert.alert(
        'Token Test Result',
        `Token: ${token ? 'Generated ✅' : 'Failed ❌'}\nLength: ${token ? token.length : 0}\nSynced: ${tokenInfo?.isSynced ? 'Yes' : 'No'}`,
        [
          { text: 'Copy Token', onPress: () => this.copyToClipboard(token) },
          { text: 'OK' }
        ]
      );

      return { token, tokenInfo };
    } catch (error) {
      console.error('❌ Token test failed:', error);
      Alert.alert('Token Test Failed', error.message);
      return null;
    }
  }

  // Test notification service initialization
  static async testServiceInitialization() {
    console.log('🧪 Testing notification service initialization...');
    
    try {
      const isInitialized = FirebaseNotificationService.isServiceInitialized();
      
      if (!isInitialized) {
        console.log('🔄 Service not initialized, initializing now...');
        const success = await FirebaseNotificationService.initialize();
        
        Alert.alert(
          'Service Initialization Test',
          `Initialization: ${success ? 'Success ✅' : 'Failed ❌'}`,
          [{ text: 'OK' }]
        );
        
        return success;
      } else {
        Alert.alert(
          'Service Initialization Test',
          'Service already initialized ✅',
          [{ text: 'OK' }]
        );
        
        return true;
      }
    } catch (error) {
      console.error('❌ Service initialization test failed:', error);
      Alert.alert('Service Test Failed', error.message);
      return false;
    }
  }

  // Test local notification display
  static async testLocalNotification() {
    console.log('🧪 Testing local notification display...');
    
    try {
      // Simulate a received notification
      const mockNotification = {
        notification: {
          title: '🧪 Test Notification',
          body: 'This is a test notification from UEST app'
        },
        data: {
          type: 'TEST_NOTIFICATION',
          testId: Date.now().toString()
        }
      };

      // Use the service's foreground notification handler
      FirebaseNotificationService.handleForegroundNotification(mockNotification);
      
      console.log('✅ Local notification test completed');
      return true;
    } catch (error) {
      console.error('❌ Local notification test failed:', error);
      Alert.alert('Local Notification Test Failed', error.message);
      return false;
    }
  }

  // Test token refresh
  static async testTokenRefresh() {
    console.log('🧪 Testing token refresh...');
    
    try {
      const oldToken = await FCMTokenService.getCurrentToken();
      console.log('🔄 Old token:', oldToken ? oldToken.substring(0, 20) + '...' : 'None');
      
      const newToken = await FCMTokenService.refreshToken();
      console.log('🆕 New token:', newToken ? newToken.substring(0, 20) + '...' : 'None');
      
      const refreshed = oldToken !== newToken;
      
      Alert.alert(
        'Token Refresh Test',
        `Old Token: ${oldToken ? 'Present' : 'None'}\nNew Token: ${newToken ? 'Present' : 'None'}\nRefreshed: ${refreshed ? 'Yes ✅' : 'No ❌'}`,
        [{ text: 'OK' }]
      );

      return { oldToken, newToken, refreshed };
    } catch (error) {
      console.error('❌ Token refresh test failed:', error);
      Alert.alert('Token Refresh Test Failed', error.message);
      return null;
    }
  }

  // Run all tests
  static async runAllTests() {
    console.log('🧪 Running all notification tests...');
    
    const results = {
      permissions: null,
      token: null,
      service: null,
      localNotification: null,
      tokenRefresh: null,
    };

    try {
      // Test 1: Permissions
      console.log('📱 Test 1: Permissions');
      results.permissions = await this.testPermissions();
      await this.delay(1000);

      // Test 2: Service Initialization
      console.log('🔧 Test 2: Service Initialization');
      results.service = await this.testServiceInitialization();
      await this.delay(1000);

      // Test 3: Token Generation
      console.log('🔑 Test 3: Token Generation');
      results.token = await this.testTokenGeneration();
      await this.delay(1000);

      // Test 4: Local Notification
      console.log('📨 Test 4: Local Notification');
      results.localNotification = await this.testLocalNotification();
      await this.delay(1000);

      // Test 5: Token Refresh
      console.log('🔄 Test 5: Token Refresh');
      results.tokenRefresh = await this.testTokenRefresh();

      // Show summary
      this.showTestSummary(results);
      
      return results;
    } catch (error) {
      console.error('❌ Test suite failed:', error);
      Alert.alert('Test Suite Failed', error.message);
      return results;
    }
  }

  // Show test summary
  static showTestSummary(results) {
    const summary = `
📊 NOTIFICATION TEST SUMMARY

✅ Permissions: ${results.permissions?.enabled ? 'PASS' : 'FAIL'}
✅ Service Init: ${results.service ? 'PASS' : 'FAIL'}  
✅ Token Gen: ${results.token?.token ? 'PASS' : 'FAIL'}
✅ Local Notif: ${results.localNotification ? 'PASS' : 'FAIL'}
✅ Token Refresh: ${results.tokenRefresh?.refreshed ? 'PASS' : 'FAIL'}

Platform: ${Platform.OS}
Token Length: ${results.token?.token?.length || 0}
    `.trim();

    console.log(summary);
    
    Alert.alert(
      'Test Summary',
      summary,
      [{ text: 'OK' }]
    );
  }

  // Helper: Copy to clipboard (basic implementation)
  static copyToClipboard(text) {
    if (text) {
      // In a real app, you'd use @react-native-clipboard/clipboard
      console.log('📋 Token copied to console:', text);
      Alert.alert('Token Copied', 'Token has been logged to console');
    }
  }

  // Helper: Delay function
  static delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  // Test notification with specific data
  static async testNotificationWithData(type, title, body, data = {}) {
    console.log(`🧪 Testing notification with type: ${type}`);
    
    const mockNotification = {
      notification: { title, body },
      data: { type, ...data }
    };

    FirebaseNotificationService.handleForegroundNotification(mockNotification);
    
    return mockNotification;
  }

  // Test different notification types
  static async testNotificationTypes() {
    const types = [
      {
        type: 'STUDENT_ACCOUNT_CREATED',
        title: '🎉 Welcome to UEST!',
        body: 'Your account has been created successfully'
      },
      {
        type: 'STUDENT_COIN_PURCHASE',
        title: '💰 Coins Purchased',
        body: 'You have successfully purchased 100 coins'
      },
      {
        type: 'STUDENT_UWHIZ_PARTICIPATION',
        title: '📚 Exam Results',
        body: 'Your exam results are now available'
      }
    ];

    for (const notif of types) {
      await this.testNotificationWithData(notif.type, notif.title, notif.body);
      await this.delay(2000);
    }

    Alert.alert('Notification Types Test', 'All notification types tested!');
  }
}

export default NotificationTester;
