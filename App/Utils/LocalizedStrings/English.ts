export default {
  ALL: {
    ISREQUIRED: 'is required .',
    DELETE: 'Delete',
    <PERSON>NC<PERSON>: 'Cancel',
  },
  Login: {
    USER: 'User',
    EMAIL: 'Email',
    USERNAME: 'Username',
    PASSWORD: 'Password',
    LOGI<PERSON>: 'Login',
    REGISTERUSER: 'Register User',
    PLEASENETEREMAIL: 'Please enter valid Email address.',
    PLEASENTERPASSWORD: 'Please enter password.',
    INVALIDCRED: 'Invalid Credential. Please Try Again',
    FORGOTPASSWORD: 'Forgot Password?',
    PASSWORDMUSTBESIXCHARACTER: 'Password must be at least 6 characters long.',
    LOGINSUCCESSFUL: 'Login successful.',
    S<PERSON><PERSON><PERSON><PERSON>WENTWRONG: 'Something went wrong.',
    INVALIDCREDENTIALS: 'Invalid Credentials.',
    USERNOTFOUND: 'User Not Found.',
    LOGINFAILED: 'Login failed.',
    NETW<PERSON>KERRORORSERVERUNREACHABLE: 'Network error or server unreachable.',
    UNEXPECTE<PERSON>RROROCCURED: 'Unexpected error occurred.',
    CONTIN<PERSON><PERSON>THGOOG<PERSON>: 'Continue with <PERSON>',
  },

  Registration: {
    REGISTER: 'Register',
    FIRSTNAME: 'First Name',
    ENTERFIRSTNAME: 'Enter first name',
    LASTNAME: 'Last Name',
    ENTERLASTNAME: 'Enter last name',
    EMAIL: 'Email',
    ENTEREMAIL: 'Enter email',

    CONTACAT: 'Contact Number',
    ENTERCONTACT: 'Enter Contact Number',
    PASSWORD: 'Password',
    ENTERPASSWORD: 'Enter password',
    ALREADYHAVEACCOUNT: 'Already have an account? Login',
    PLEASEENTERFIRSTNAME: 'Please enter first name.',
    PLEASEENTERLASTNAME: 'Please enter last name.',
    PLEASEENTEREMAIL: 'Please enter email.',
    PLEASEENTERPASSWORD: 'Please enter password.',
    PLEASEENTERCONTACT: 'Please enter contact.',
    PASSWORDLENGTH: 'Password must be at least 6 characters long.',
    REGISTRATIONSUCCESSFUL: 'Registration successful.',
    SOMETHINGWENTWRONG: 'Something went wrong.',
    INVALIDCREDENTIALS: 'Invalid Credentials.',
    USERNOTFOUND: 'User Not Found.',
    REGISTRATIONFAILED: 'Registration failed.',
    NETWORKERRORORSERVERUNREACHABLE: 'Network error or server unreachable.',
    UNEXPECTEDERROROCCURED: 'Unexpected error occurred.',
    STUDENTALREADYEXIST: 'Student Already Exist',
    INTERNALSERVERERROR: 'Internal Server Error',
    RECAPCHAVALIDATIONFAILED: 'reCAPTCHA validation failed.',
  },
  Home: {
    HOME: 'Home',
    SETTING: 'Setting',
    FINDCLASS: 'Find Class',
    UWHIZQUIZ: 'U-WHIZ',
    DASHBOARD: 'Dashboard',
    WELCOME: 'Welcome',
    TUTORHOME: 'TutorHome',
    PROFILE: 'Profile',
    DAILYQUIZ: 'Daily Quiz',
    PROFILEINCOMPLETE: 'Profile is incomplete',
    WALLET: 'Wallet',
    SEARCH: 'Search',
    EXAM:'Exam',
  },
  Setting: {
    SETTING: 'Setting',
    MODE: 'Mode',
    LOGOUT: 'Logout',
    AREYOUSUREYOUWANTTOLOGOUT: 'Are you sure you want to logout?',
    CANCEL: 'Cancel',
  },
  ClassList: {
    LIST: 'Classes',
    PROFILE: 'View Profile',
    MESSAGE: 'Message',
    COMMINGSOON:
      "'Coming Soon', 'This feature will be available in a future update!'",
  },
  ClassCategoryList: {
    FILTERCLASSES: 'Filter Classes',
    CATEGORY: 'Category',
    SELECTCATEGORY: 'Select Category',
    BOARDTYPE: 'Board Type',
    SELECTBOARDTYPE: 'Select Board Type',
    MEDIUM: 'Medium',
    SELECTMEDIUM: 'Select Medium',
    DETAILS: 'Details',
    SELECTDETAILS: 'Select Details',
    SECTION: 'Section',
    SELECTSECTION: 'Select Section',
    SUBJECT: 'Subject',
    SELECTSUBJECT: 'Select Subject',
    COACHINGTYPE: 'Coaching Type',
    SELECTCOACHINGTYPE: 'Select Coaching Type',
    RESET: 'Reset',
    APPLY: 'Apply',
  },
  Profile: {
    PROFILE: 'Profile',
    REVIEW: 'Review',
    RATING: 'Rating',
    EDUCATIONDETAILSTITLE: '🏫 Education Details',
    WORKTITLE: '💼 Work Experience',
    CERTIFICATETITLE: '📜 Certificates',
    TUTIONCLASSESTITLE: '👨‍🏫 Tuition Classes',
    SAVETOLIST: 'Save To List',
    SENDMESSAGE: 'Send Message',
    NODATAAVAILABLE: 'No Data Available',
    UNIVERSITY: 'University : ',
    DEGREE: 'Degree : ',
    PASSOUTYEAR: 'Passout Year : ',
    ROLE: 'Role : ',
    FROM: 'From : ',
    TO: 'To : ',
    TITLE: 'Title : ',
    EDUCATION: 'Education : ',
    COACHINGTYPE: 'Coaching Type : ',
    DETAILS: 'Details : ',
    BOARDTYPE: 'Board Type : ',
    SUBJECT: 'Subject : ',
    MEDIUN: 'Medium : ',
    SECTION: 'Section : ',
    SOMETHINGWENTWRONG: 'Something went wrong.',
  },
  StudentProfile: {
    PROFILE: 'Profile',
    COMPLETEYOURPROFILE: 'Complete your profile information',
    FIRSTNAME: 'First Name',
    ENTERYOURFIRSTNAME: 'Enter Your first name',
    LASTNAME: 'Last Name',
    ENTERYOURLASTNAME: 'Enter Your Last name',
    EMAIL: 'Email',
    ENTERYOUREMAIL: 'Enter Your Email',
    CONTACT: 'Contact',
    ENTERYOURCONTACT: 'Enter Your Contact',
    MEDIUM: 'Medium',
    SELECTMEDIUM: 'Select Medium',
    CLASSROM: 'Classroom (Standard)',
    SELECTCLASSROOM: 'Select Classroom',
    DATEOFBIRTH: 'Date of Birth',
    DOBDESCRIPTION: 'Your date of birth will be verified with your documents',
    PROFILEPHOTO: 'Profile Photo',
    PHOTODESCRIPTION: 'Take a clear photo of your face for your profile',
    OPENGALLERY: 'Open Gallery',
    OPENCAMERA: 'Open Camera',
    CANCEL: 'Cancel',
    SAVE: 'Save',
    UPLOADPROFILEPHOTO: 'Upload Profile Photo',
    UPLOADCLASSESLOGO: 'Upload Classes Logo',
    OR: 'OR',
    IDENTITYDOCUMENT: 'Identity Document',
    IDENTITYDOCUMENTDESCRIPTION:
      'Upload Aadhar card, Bonafide certificate, Leaving certificate, ID card or any other document that contains your birthdate.',
    CLICKTOUPLOAD: 'Click to upload',
    FILETYPEDESCRIPTION: 'PDF, PNG, JPG or JPEG (MAX. 5MB)',
    SCHOOLINFORMATION: 'School Information',
    ENTERYOURSCHOOLDETAILSANDADDRESS: 'Enter your school details and address',
    ENTERYOURSCHOOLNAME: 'Enter your school name',
    ENTERYOURFULLADDRESS: 'Enter your full address',
    TAPTOVIEWDOCUMENT: 'Tap to view document',
    NODOCUMENTUPLOADED: 'No document uploaded',
    NOPROFILEPHOTO: 'No profile photo uploaded',
    SUBMIT: 'Submit',
    PLEASEENTERFIRSTNAME: 'Please enter first name',
    PLEASEENTERLASTNAME: 'Please enter last name',
    PLEASEENTEREMAIL: 'Please enter Email',
    PLEASEENTERCONTACT: 'Please enter Contact',
    PLEASESELECTDATEOFBIRTH: 'Please select Date of Birth',
    PLEASEENTERSCHOOLNAME: 'Please enter School Name',
    PLEASEENTERFULLADDRESS: 'Please enter Full Address',
    SELECTYOURBIRTHDAY: 'Select Your birthday',
    PLEASEENTERPROFILEPHOTO: 'Please enter Profile Photo',
    PLEASEENTERDOCUMENT: 'Please Enter Document',
  },
  Exam: {
    UPCOMING: 'Upcoming',
    EXAMS: 'Exams',
    STPRIZE: 'st Prize : ',
    RS: 'Rs.',
    TOTALQUESTION: 'Total Question',
    DURATION: 'Duration',
    MIN: 'Min',
    MARKS: 'Marks',
    COINS: 'Coins',
    ENTRYFEE: 'Entry Fee',
    CLASSESJOINED: 'Classes Joined',
    STUDENTJOINED: 'Student Joined',
    VIEWRESULT: 'View Result',
    VIEWDETAILS: 'View Details',
    YOUARELATE: 'You are late',
    UPCOMINGEXAM: 'Upcoming Exams',
    MAINPRICE: '21000',
    APPLYNOW: 'Apply Now',
    AREYOUSURE: 'Are you sure?',
    YESAPPLY: 'Yes, Apply',
    CANCEL: 'Cancel',
  },
  ViewResult: {
    RESULT: 'Result',
    EXAMRANKING: 'Exam Ranking',
  },
  UseAppAs: {
    STUDENT: 'Student',
    TUTOR: 'Tutor',
    PICKYOURROLETOCONTINUE: 'Pick Your Role To Continue',
  },
  ForgotPassword: {
    EMAIL: 'Email',
    ENTEREMAIL: 'Enter email',
    PLEASEENTEREMAIL: 'Please enter email.',
    DESCRIPTION: ' * Enter your email to receive a password reset link',
    SENDRESETLINK: 'Send Reset Link',
    SOMETHINGWENTWRONG: 'Something went wrong.',
  },
  Blog: {
    BLOG: 'Blog',
    ADDBLOG: 'Add Blog',
    CREATEBLOG: 'Create New Blog',
    TITLE: 'Title',
    IMAGE: 'Image',
    DESCRIPTION: 'Description',
    ENTERTITLE: 'Enter blog title',
    UPLOADIMAGE: 'Upload Image',
    ENTERDESCRIPTION: 'Enter description (50-500 characters)',
    CANCEL: 'Cancel',
    CREATE: 'Create',
    UPDATE: 'Update',
    DELETE: 'Delete',
    NOBLOGPOSTFOUND: 'No blog posts found.',
    NOIMAGE: 'No Image',
    REMOVEDMESSAGE: 'Class removed from wishlist successfully',
  },
  CreateTution: {
    CREATETUTUION: 'Create Tution',
  },
  About: {
    ABOUT: 'About',
    USERNAME: 'Username',
    ENTERUSERNAME: 'Enter username',
    FIRSTNAME: 'First Name',
    ENTERFIRSTNAME: 'Enter first name',
    LASTNAME: 'Last Name',
    ENTERLASTNAME: 'Enter last name',
    CLASSNAME: 'Class Name',
    ENTERCLASSNAME: 'Enter class name',
    EMAIL: 'Email',
    ENTEREMAIL: 'Enter email',
    BIRTHDATE: 'Birth Date',
    DATEOFBIRTH: 'Date of Birth',
    BIRTHDATEPLACEHOLDER: 'DD/MM/YYYY',
    CONTACTNUMBER: 'Contact No.',
    ENTERCONTACTNUMBER: 'Enter contact number',
    ICONFIRMIAMOVEREIGHTEEN: "I confirm I'm over 18",
    UPDATEPROFILE: 'Update Profile',
  },
  Certificate: {
    CERTIFICATE: 'Certificate',
    ADDMORECERTIFICATE: 'Add More Certificate',
    SAVECERTIFICATES: 'Save Certificates',
    DONOTHAVECERTIFICATE: 'I don`t have certificate',
    TITLE: 'Title',
    CERTIFICATENAME: 'Certificate Name',
    UPLOADCERTIFICATE: 'Upload Certificate',
    CERTIFICATENAMEPLACEHOLDER: 'e.g. Teaching Excellence',
    REMOVE: 'Remove',
  },
  Description: {
    DESCRIPTION: 'Description',
    CATCHYHEADLINE: 'Catchy Headline',
    HEADLINEPLACEHOLDER: 'e.g Math Tutor with 7+ Years of Experience',
    TUTORBIO: 'Tutor Bio',
    BIOPLACEHOLDER: `1. Introduce yourself \n2. Teaching experience \n3. Motivate potential students `,
    SAVEPROFILE: 'Save Profile',
    ISREQUIRED: 'is required.',
  },
  Education: {
    EDUCATION: 'Education',
    IDONTHAVEDEGREE: 'I don`t have a degree',
    ADDNEWEDUCATION: 'Add New Education',
    SAVEEDUCATION: 'Save Education',
    UNIVERSITY: 'University',
    UNIVERSITYPLACEHOLDER: 'e.g. Delhi University',
    DEGREE: 'Degree',
    SELECTDEGREE: 'Select Degree',
    DEGREEPLACEHOLDER: 'e.g. Bachelor of Science',
    DEGREETYPE: 'Degree Type',
    DEGREETYPEPLACEHOLDER: 'e.g. Undergraduate',
    PASSOUTYEAR: 'Passout Year',
    PASSOUTYEARPLACEHOLDER: 'e.g. 2020',
    DEGREECERTIFICATE: 'Degree Certificate (PDF/Image)',
    CHOOSEFILE: 'Choose File',
    SELECTDEGREECERTIFICATE: 'Select Degree Certificate',
    REMOVE: 'Remove',
  },
  Experience: {
    EXPERIENCE: 'Experience',
    IDONTHAVEEXPERIENCE: 'I don`t have any experience',
    ADDMOREEXPERIENCE: 'Add More Experience',
    SAVEEXPERIENCE: 'Save Experience',
    TITLE: 'Title',
    FROM: 'From',
    TO: 'To',
    EXPERIENCETITLE: 'Experience Title',
    EXPERIENCETITLEPLACEHOLDER: 'e.g Senior Teacher at XYZ',
    UPLOADEXPERIENCEPROOF: 'Upload Experience Proof',
    SELECTSTARTDATE: 'Select Start Date',
    SELECTENDDATE: 'Select End Date',
  },
  PhotoAndLogo: {
    PHOTOANDLOGO: 'Photo & Logo',
    PROFILEPHOTO: 'Profile Photo',
    CLASSESLOGO: 'Classes Logo',
    UPLOADPROFILEPHOTO: 'Upload Profile Photo',
    UPLOADCLASSESLOGO: 'Upload Classes Logo',
    UPLOADIMAGE: 'Upload Images',
    DESCRIPTION1: 'Upload your image and classes logo',
    DESCRIPTION2: 'Choose a photo that will help wheres get to know you',
    NOIMAGES: 'No Images',
    SELECTIMAGE: 'Please select at least one image.',
    SAVED: 'Saved',
    IMAGESAVEDMESSAGE: 'Images have been saved locally.',
    PERMISSIONDENIED: 'Permission Denied',
    CANNOTACCESSGALLERY: 'Cannot access gallery.',
    CANCELLED: 'Cancelled',
    CANCELLEDMESSAGE: 'Image selection was cancelled.',
    STORAGEPERMISSIONTITLE: 'Storage Permission',
    APPNEEDSACCESSTOYOURPHOTOGALLERY: 'App needs access to your photo gallery',
    ASKMELATER: 'Ask Me Later',
    CANCEL: 'Cancel',
    OK: 'OK',
  },
  TutionClass: {
    TUITIONDELETESUCCESS: 'Tuition Class Deleted successfully',
    TUITIONCLASSFAIL: 'Tuition Class Faild to delete',
    TUITIONCLASSERROR: 'Tuition Class Delete Erorr',
    CATEGORY: 'Category',
    TUTIONCLASS: 'Tution Class',
    ADDANOTHERTUTION: 'Add Another Tution',
    SAVETUTIONCLASSDETAILS: 'Save Tution Class Details',
    ADD: 'Add New Tution Class Details',
    SUBMIT: 'Submit',
    DELETE: 'Delete',
    SAVETUITIONCLASSLIST: 'Save Tuition Class List',
    EDUCATION: 'Education',
    BOARDTYPE: 'Board Type',
    MEDIUN: 'Medium',
    SUBJECT: 'Subject',
    SECTION: 'Section',
    MEDIUM: 'Medium',
    COASHINGTYPE: 'Coaching Type',
    DETAILS: 'Details',
    TUITIONDATASAVEDMESSAGE: "Success', 'Tuition data saved successfully!",
    TUITIONDATASAVEDERROR: "Error', 'Failed to save tuition data.",
    CONFIRMDELETE: 'Confirm Delete',
    DELETEASKINGSTRING: 'Are you sure you want to delete this tuition record?',
  },
  Testmonials: {
    TESTIMONIALS: 'Testmonials',
    WRITETESTMONIAL: 'Write a Testmonial',
    YOURRATING: 'Your Rating',
    YOURMESSAGE: 'Your Message',
    SHAREYOUREXPERIENCE: 'Share Your Experience...',
    MESSAGE: 'Message',
    RATING: 'Rating',
    SUBMITTESTIMONIALS: 'Submit Testimonials',
    ISREQUIRED: 'is required.',
    POSTEDON: 'Posted on',
    APPROVED: 'Approved',
    PANDING: 'Pending',
    REMOVEFROMTESTMONIAL: 'Remove from Testmonials',
    CONFIRMDELETE: 'Are you sure you want to delete Testmonial Data?',
    REMOVEDMESSAGE: 'Removed from Testmonials Data successfully',
    SUCCESSMESSAGE: 'Testimonial submitted successfully!',
    ERRORMESSAGE: 'Failed to submit testimonial. Please try again.',
  },
  Review: {
    REVIEW: 'Add Review',
    WRITEREVIEW: 'Write a Review',
    YOURREVIEW: 'Your Review',
  YOURMESSAGE: 'Your Message',
  WRITEREVIEWS:'Write Review',
    SHAREYOUREXPERIENCE: 'Share Your Experience...',
    MESSAGE: 'Message',
    MESSAGEREQUIRED: 'Message Required',
    MINIMUMRATING: 'Minimum Select 1 Star Required',
    SUBMITREVIEW: 'Submit Review',
    POSTEDON: 'Posted on',
  },
  Thoughts: {
    THOUGHTS: 'Thoughts',
    THOUGHT: 'Thought',
    THOUGHT1: 'Thought :',
    CREATEDAT: 'Created at :',
    CREATETHOUGHT: 'Create Thought',
    ADDTHOUGHT: 'Add Thought',
    ENTERTHOUGHT50500CHARACTERS: 'Enter Thought (50-500 characters)',
    DELETE: 'Delete',
    CANCEL: 'Cancel',
    SAVE: 'Save',
    NODATAFOUND: 'No Data Found',
    FETCHERROR: 'Failed to load thoughts',
    THOUGHTSAVEDSUCCESS: 'Thought saved successfully',
    THOUGHTSAVEERROR: 'Failed to save thought',
    THOUGHTUPDATESUCCESS: 'Thought saved successfully',
    THOUGHTUPDATEERROR: 'Failed to save thought',
    THOUGHTDELETEDSUCCESS: 'Thought deleted successfully',
    THOUGHTDELETEERROR: 'Failed to delete thought',
    INPUTVALIDATIONERROR: 'Thought must be between 50 and 500 characters',
    CONFIRMDELETE: 'Confirm Delete',
    DELETEASKINGSTRING: 'Are you sure you want to delete this thought?',
    EDIT: 'Edit',
  },
  Wishlist: {
    WHISLIST: 'Wishlist',
    PROFILE: 'Profile',
    DELETE: 'Delete',
    SAVE: 'Save',
    NOWISHLISTDATA: 'No Wishlist Data Found',
    REMOVEDMESSAGE: 'Class removed from wishlist successfully',
    FAILEDMESSAGE: 'Failed to remove class. Please try again.',
    REMOVEFROMWISHLIST: 'Remove from Wishlist',
    CONFIRMDELETE:
      'Are you sure you want to delete this class from your wishlist?',
    FAILEDTODELETE: 'Failed to delete',
  },
  PaymentList: {
    HEADER: 'Payment',
    STUDENTHEADING: 'Student Uest Coin Balance',
    ENTERAMOUNT: 'Enter amount (₹1 - ₹10,000)',
    ADDCOINS: 'Add Coins',
    NOTRANSACTIONFOUND:
      'No transactions found. Start earning or spending coins!',
    ENTERVALIDAMOUNT: 'Please enter a valid amount.',
    FAILDINITIATEPAYMENT: 'Failed to initiate payment',
    PAYMENTVERIFIED: 'Payment verified successfully!',
    VARIFICATIONFAILD:
      'Verification Failed, Your payment could not be verified.',
    PAYMENTFAILED: 'Payment Failed',
    SOMETHINGWENTWRONG: 'Something went wrong. Please try again.',
  },
  Refferal: {
    REFERRAL: 'Referral',
    REFFERALDASHBOARD: 'Referral Dashboard',
    SHAREUESTINWITHFRIENDSANDTRACKYOURREFERRALS:
      'Share Uest.in with friends and track your referrals',
    TOTALREFERRALS: 'Total Referrals',
    STUDENTSREFERRED: 'Students Referred',
    CLASSESREFERRED: 'Classes Referred',
    MYREFERRALLINKS: 'My Referral Links',
    SHARETHESELINKSTOREFERNNEWSTUDENTSANDCLASSES:
      'Share these links to refer new students and classes to Uest.in',
    STUDENTREGISTRATIONLINK: 'Student Registration Link',
    CLASSREGISTRATIONLINK: 'Classes Registration Link',
    REFERRALHISTORY: 'Referral History',
    TRACKALLYOURSUCCESSFULREFERRALS: 'Track all your successful referrals',
    STARTDATE: 'Start Date',
    SELECTSTARTDATE: 'Select Start Date',
    ENDDATE: 'End Date',
    SELECTENDDATE: 'Select End Date',
    FILTER: 'Filter',
    NAME: 'Name :',
    EMAIL: 'Email :',
    TYPE: 'Type :',
    DATE: 'Date :',
  },
};
