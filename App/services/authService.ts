import axiosInstance from '../config/axios';

export async function tutorLogin(data: any) {
  const response = await axiosInstance.post('/auth-client/login', data);
  return response.data;
}

export async function tutorRegister(data: any) {
  const response = await axiosInstance.post('/auth-client/register', data);
  return response.data;
}

export async function registerStudent(data: {
  firstName: string;
  lastName: string;
  contactNo: string;
  referralCode?: string;
  flowType?: 'registration';
}) {
  const response = await axiosInstance.post('/student/register', data);
  return response.data;
}

export async function loginStudent(data: { 
  contactNo: string; 
  email?: string;
  flowType?: 'login';
}) {
  const response = await axiosInstance.post('/student/login', data);
  return response.data;
}

export async function verifyOtp(data: {
  contactNo: string;
  otp: string;
  email?: string;
  firstName?: string;
  lastName?: string;
  flowType?: 'login' | 'registration';
}) {
  const response = await axiosInstance.post('/student/verify-otp', data);
  return response.data;
}

export async function resendOtp(data: {
  contactNo: string;
  firstName?: string;
  flowType?: 'login' | 'registration';
}) {
  const response = await axiosInstance.post('/student/resend-otp', data);
  return response.data;
}

export async function continueWithEmail(data: { 
  email: string;
  flowType?: 'login';
}) {
  const response = await axiosInstance.post('/student/continue-with-email', data);
  return response.data;
} 