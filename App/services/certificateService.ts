import axiosInstance from '../config/axios';

export async function uploadCertificates(
  noCertificates: boolean,
  certificates: { title: string; file: any }[]
) {
  const formData = new FormData();
  formData.append('noCertificates', noCertificates ? 'true' : 'false');
  if (!noCertificates && certificates.length > 0) {
    const certData = certificates.map(cert => ({ title: cert.title }));
    formData.append('certificates', JSON.stringify(certData));
    certificates.forEach((cert, index) => {
      if (cert.file && cert.file.uri) {
        formData.append('files', {
          uri: cert.file.uri,
          name: cert.file.name || `certificate_${index}.pdf`,
          type: cert.file.type || 'application/pdf',
        } as any);
      }
    });
  }
  const response = await axiosInstance.post('/classes-profile/certificates', formData, {
    headers: { 'Content-Type': 'multipart/form-data' },
  });
  return response.data;
} 