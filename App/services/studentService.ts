import axiosInstance from '../config/axios';

export async function studentRegister(data: any) {
  const response = await axiosInstance.post('/student/register', data);
  return response.data;
}

export async function googleLogin(data: any) {
  const response = await axiosInstance.post('/student/google-auth', data);
  return response.data;
}

export async function getCurrentStudent() {
  const response = await axiosInstance.get('/student/me');
  return response.data;
} 