import axiosInstance from '../config/axios';

export async function getLeaderBoard(page = 1, limit = 9, filter: string) {
  const response = await axiosInstance.get(`mock-exam-leaderboard/leaderboard/${filter}?page=${page}&limit=${limit}`, {
    headers: { 'Server-Select': 'uwhizServer' },
  });
  return response.data;
}

export async function getDailyResults(studentId: string) {
  const response = await axiosInstance.get(`mock-exam-result/${studentId}`, {
    headers: { 'Server-Select': 'uwhizServer' },
  });
  return response.data;
}
