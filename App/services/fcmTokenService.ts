// FCM Token Management Service for UEST app
import AsyncStorage from '@react-native-async-storage/async-storage';
import { axiosInstance } from './axiosInstance';
import messaging from '@react-native-firebase/messaging';

interface TokenData {
  token: string;
  lastUpdated: string;
  deviceId: string;
  platform: string;
}

class FCMTokenService {
  private static instance: FCMTokenService;
  private currentToken: string | null = null;
  private isTokenSynced: boolean = false;

  private constructor() {}

  static getInstance(): FCMTokenService {
    if (!FCMTokenService.instance) {
      FCMTokenService.instance = new FCMTokenService();
    }
    return FCMTokenService.instance;
  }

  // Get current FCM token
  async getCurrentToken(): Promise<string | null> {
    try {
      if (this.currentToken) {
        return this.currentToken;
      }

      const token = await messaging().getToken();
      if (token) {
        this.currentToken = token;
        await this.storeTokenLocally(token);
        return token;
      }
      
      return null;
    } catch (error) {
      console.error('❌ Error getting FCM token:', error);
      return null;
    }
  }

  // Store token locally
  private async storeTokenLocally(token: string): Promise<void> {
    try {
      const tokenData: TokenData = {
        token,
        lastUpdated: new Date().toISOString(),
        deviceId: await this.getDeviceId(),
        platform: require('react-native').Platform.OS,
      };

      await AsyncStorage.setItem('fcm_token_data', JSON.stringify(tokenData));
      console.log('💾 FCM token stored locally');
    } catch (error) {
      console.error('❌ Error storing FCM token locally:', error);
    }
  }

  // Get stored token data
  async getStoredTokenData(): Promise<TokenData | null> {
    try {
      const storedData = await AsyncStorage.getItem('fcm_token_data');
      if (storedData) {
        return JSON.parse(storedData);
      }
      return null;
    } catch (error) {
      console.error('❌ Error getting stored token data:', error);
      return null;
    }
  }

  // Send token to server
  async syncTokenWithServer(token?: string): Promise<boolean> {
    try {
      const fcmToken = token || await this.getCurrentToken();
      if (!fcmToken) {
        console.log('❌ No FCM token available to sync');
        return false;
      }

      console.log('📤 Syncing FCM token with server...');

      // Check if user is authenticated
      const userToken = await AsyncStorage.getItem('token');
      if (!userToken) {
        console.log('⚠️ User not authenticated, storing token for later sync');
        await this.storeTokenLocally(fcmToken);
        return false;
      }

      // Send token to your backend
      const response = await axiosInstance.post('/users/fcm-token', {
        fcmToken,
        deviceId: await this.getDeviceId(),
        platform: require('react-native').Platform.OS,
        appVersion: require('../../package.json').version,
      });

      if (response.status === 200) {
        console.log('✅ FCM token synced with server successfully');
        this.isTokenSynced = true;
        
        // Update local storage with sync status
        const tokenData = await this.getStoredTokenData();
        if (tokenData) {
          tokenData.lastUpdated = new Date().toISOString();
          await AsyncStorage.setItem('fcm_token_data', JSON.stringify(tokenData));
        }
        
        return true;
      } else {
        console.log('❌ Failed to sync FCM token with server');
        return false;
      }
    } catch (error) {
      console.error('❌ Error syncing FCM token with server:', error);
      
      // If it's a network error, store for later sync
      if (error.code === 'NETWORK_ERROR' || error.message.includes('Network')) {
        console.log('📱 Network error, will retry later');
        const fcmToken = token || await this.getCurrentToken();
        if (fcmToken) {
          await this.storeTokenLocally(fcmToken);
        }
      }
      
      return false;
    }
  }

  // Sync token after user login
  async syncTokenAfterLogin(): Promise<boolean> {
    try {
      console.log('🔐 User logged in, syncing FCM token...');
      
      const storedTokenData = await this.getStoredTokenData();
      if (storedTokenData) {
        return await this.syncTokenWithServer(storedTokenData.token);
      } else {
        // Get fresh token and sync
        const token = await this.getCurrentToken();
        if (token) {
          return await this.syncTokenWithServer(token);
        }
      }
      
      return false;
    } catch (error) {
      console.error('❌ Error syncing token after login:', error);
      return false;
    }
  }

  // Remove token from server (on logout)
  async removeTokenFromServer(): Promise<boolean> {
    try {
      console.log('🚪 Removing FCM token from server...');
      
      const tokenData = await this.getStoredTokenData();
      if (!tokenData) {
        console.log('ℹ️ No stored token to remove');
        return true;
      }

      const response = await axiosInstance.delete('/users/fcm-token', {
        data: {
          fcmToken: tokenData.token,
          deviceId: tokenData.deviceId,
        }
      });

      if (response.status === 200) {
        console.log('✅ FCM token removed from server successfully');
        
        // Clear local storage
        await AsyncStorage.removeItem('fcm_token_data');
        this.currentToken = null;
        this.isTokenSynced = false;
        
        return true;
      } else {
        console.log('❌ Failed to remove FCM token from server');
        return false;
      }
    } catch (error) {
      console.error('❌ Error removing FCM token from server:', error);
      return false;
    }
  }

  // Get device ID (you can use react-native-device-info for better device identification)
  private async getDeviceId(): Promise<string> {
    try {
      let deviceId = await AsyncStorage.getItem('device_id');
      if (!deviceId) {
        // Generate a simple device ID (in production, use react-native-device-info)
        deviceId = `device_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        await AsyncStorage.setItem('device_id', deviceId);
      }
      return deviceId;
    } catch (error) {
      console.error('❌ Error getting device ID:', error);
      return `fallback_${Date.now()}`;
    }
  }

  // Check if token is synced with server
  isTokenSyncedWithServer(): boolean {
    return this.isTokenSynced;
  }

  // Force refresh token
  async refreshToken(): Promise<string | null> {
    try {
      console.log('🔄 Refreshing FCM token...');
      
      // Delete current token to force refresh
      await messaging().deleteToken();
      
      // Get new token
      const newToken = await messaging().getToken();
      if (newToken) {
        this.currentToken = newToken;
        await this.storeTokenLocally(newToken);
        
        // Sync with server if user is authenticated
        const userToken = await AsyncStorage.getItem('token');
        if (userToken) {
          await this.syncTokenWithServer(newToken);
        }
        
        console.log('✅ FCM token refreshed successfully');
        return newToken;
      }
      
      return null;
    } catch (error) {
      console.error('❌ Error refreshing FCM token:', error);
      return null;
    }
  }

  // Get token info for debugging
  async getTokenInfo(): Promise<any> {
    try {
      const tokenData = await this.getStoredTokenData();
      return {
        currentToken: this.currentToken ? `${this.currentToken.substring(0, 20)}...` : null,
        storedToken: tokenData ? `${tokenData.token.substring(0, 20)}...` : null,
        lastUpdated: tokenData?.lastUpdated,
        platform: tokenData?.platform,
        deviceId: tokenData?.deviceId,
        isSynced: this.isTokenSynced,
      };
    } catch (error) {
      console.error('❌ Error getting token info:', error);
      return null;
    }
  }
}

export default FCMTokenService.getInstance();
