import axiosInstance from '../config/axios';

export async function updateAbout(data: any) {
  const response = await axiosInstance.post('/classes-profile/about', data);
  return response.data;
}

export async function updateDescription(data: any) {
  const response = await axiosInstance.post('/classes-profile/description', data);
  return response.data;
}

export async function updatePhotoAndLogo(photoUri: string | null, logoUri: string | null) {
  const formData = new FormData();
  if (photoUri) {
    formData.append('profilePhoto', {
      uri: photoUri,
      name: 'profile_photo.jpg',
      type: 'image/jpeg',
    } as any);
  }
  if (logoUri) {
    formData.append('classesLogo', {
      uri: logoUri,
      name: 'class_logo.jpg',
      type: 'image/jpeg',
    } as any);
  }
  const response = await axiosInstance.post('/classes-profile/images', formData, {
    headers: { 'Content-Type': 'multipart/form-data' },
  });
  return response.data;
} 