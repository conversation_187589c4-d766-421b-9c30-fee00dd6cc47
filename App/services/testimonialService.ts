import axiosInstance from '../config/axios';

export async function getTestimonialData(classId: string) {
  const response = await axiosInstance.get(`/testimonials/class/${classId}`);
  return response.data;
}

export async function addTestimonialData(data: any) {
  const response = await axiosInstance.post('/testimonials', data);
  return response.data;
}

export async function deleteTestimonialData(id: string) {
  const response = await axiosInstance.delete(`/testimonials/${id}`);
  return response.data;
} 