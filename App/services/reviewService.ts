import axiosInstance from '../config/axios';

export async function getReviewData(classId: string, page = 1, limit = 1) {
  const response = await axiosInstance.get(`/reviews/class/${classId}`, {
    params: { page, limit },
  });
  return response.data;
}

export async function submitReviewData(data: any) {
  const response = await axiosInstance.post('/reviews/', data);
  return response.data;
}

export async function deleteReviewData(reviewId: string) {
  const response = await axiosInstance.delete(`/reviews/${reviewId}`);
  return response.data;
} 