import axiosInstance from '../config/axios';

export async function getExamList(page = 1, limit = 9, applicantId?: string) {
  const response = await axiosInstance.get('/exams', {
    params: { page, limit, ...(applicantId ? { applicantId } : {}) },
    headers: { 'Server-Select': 'uwhizServer' },
  });
  return response.data;
}

export async function applyForExam(examId: any, applicantId: any) {
  const response = await axiosInstance.post(
    '/examApplication',
    { examId, applicantId },
    { headers: { 'Server-Select': 'uwhizServer' } }
  );
  return response.data;
} 