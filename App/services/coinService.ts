import axiosInstance from '../config/axios';

export async function getTotalCoins() {
  const response = await axiosInstance.get('/coins/get-total-coins/student');
  return response.data;
}

export async function getTransactionHistory() {
  const response = await axiosInstance.get('/coins/transaction-history/student');
  return response.data;
}

export async function createOrder(amount: number) {
  const response = await axiosInstance.post('/coins/create-order/', { amount });
  return response.data;
}

export async function verifyPayment(payload: any) {
  const response = await axiosInstance.post('/coins/verify', payload);
  return response.data;
} 