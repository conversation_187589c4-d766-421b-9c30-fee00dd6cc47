import axiosInstance from '../config/axios';

export async function getClassList(page = 1, limit = 9) {
  const response = await axiosInstance.get('/classes/approved-tutors', {
    params: { page, limit, sortByRating: true, sortByReviewCount: true },
  });
  return response.data;
}

export async function getProfileData(classId: string) {
  const response = await axiosInstance.get(`/classes/details/${classId}`);
  return response.data;
} 