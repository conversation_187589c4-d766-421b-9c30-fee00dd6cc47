import axiosInstance from '../config/axios';

export async function updateEducation(
  educationData: any[],
  files: { uri: string; name: string; type: string }[],
  noDegree: boolean
) {
  const formData = new FormData();
  formData.append('noDegree', noDegree ? 'true' : 'false');
  if (!noDegree && educationData.length > 0) {
    formData.append('education', JSON.stringify(educationData));
    files.forEach((file, index) => {
      if (file && file.uri) {
        formData.append('files', {
          uri: file.uri,
          name: file.name || `certificate_${index}`,
          type: file.type || (file.name?.endsWith('.pdf') ? 'application/pdf' : 'image/jpeg'),
        } as any);
      }
    });
  }
  const response = await axiosInstance.post('/classes-profile/education', formData, {
    headers: { 'Content-Type': 'multipart/form-data' },
  });
  return response.data;
} 