import axiosInstance from '../config/axios';

export async function getBlogData(page = 1, limit = 10) {
  const response = await axiosInstance.get('/blogs/my-blogs', {
    params: { page, limit },
  });
  return response.data;
}

export async function addBlogData(data: any) {
  const response = await axiosInstance.post('/blogs', data);
  return response.data;
}

export async function editBlogData(id: string, data: any) {
  const response = await axiosInstance.put(`/blogs/${id}`, data);
  return response.data;
} 