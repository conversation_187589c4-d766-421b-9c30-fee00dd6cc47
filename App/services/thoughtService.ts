import axiosInstance from '../config/axios';

export async function getThoughts(page = 1, limit = 10) {
  const response = await axiosInstance.get('/classes-thought', {
    params: {status: 'APPROVED', page, limit},
  });
  return response.data;
}

export async function saveThought(data: {classId: string; thoughts: string}) {
  const response = await axiosInstance.post('/classes-thought', data);
  return response.data;
}

export async function deleteThought(thoughtId: string) {
  const response = await axiosInstance.delete(`/classes-thought/${thoughtId}`);
  return response.data;
}

export async function updateThought(thoughtId: string, thoughts: string) {
  const response = await axiosInstance.put(`/classes-thought/${thoughtId}`, {
    thoughts,
  });
  return response.data;
}
