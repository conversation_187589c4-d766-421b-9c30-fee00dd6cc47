import axiosInstance from '../config/axios';

export async function getWishlistData(page = 1, limit = 10) {
  const response = await axiosInstance.get('/student-wishlist', {
    params: { page, limit },
  });
  return response.data;
}

export async function addWishlist(data: any) {
  const response = await axiosInstance.post('/student-wishlist', data);
  return response.data;
}

export async function deleteWishlistData(id: string) {
  const response = await axiosInstance.delete(`/student-wishlist/${id}`);
  return response.data;
} 