import axios from 'axios';
import AsyncStorage from '@react-native-async-storage/async-storage';

const axiosClass = axios.create({
  baseURL: 'http://192.168.31.254:8000/api/student',
});

axiosClass.interceptors.request.use(
  async (config) => {
    const token = await AsyncStorage.getItem('token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => Promise.reject(error),
);

export default axiosClass;