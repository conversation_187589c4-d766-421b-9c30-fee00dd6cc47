import { StyleSheet, Text, View } from 'react-native'
import React from 'react'
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
// import Home from '../Screens/Student/Home/Home';
import TutorHome from '../Screens/Tutor/Home/TutorHome';
// import Setting from '../Screens/Student/Setting/Setting';
import TutorProfile from '../Screens/Tutor/Profile/TutorProfile';
import TutorSetting from '../Screens/Tutor/TutorSetting/TutorSetting';
import Ionicons from 'react-native-vector-icons/Ionicons';
import { IMAGE_CONSTANT,PrimaryColors } from '../Utils/Constants';
import {useSelector} from 'react-redux';
// import {RootState} from '../Redux/themeSlice';
import { RootState } from '../Redux/store';

import strings from '../Utils/LocalizedStrings/LocalizedStrings';

const Tab = createBottomTabNavigator();

const TabNavigator = () => {
    const isDarkMode = useSelector((state: RootState) => state.theme.isDarkMode);
    const styles = isDarkMode ? DarkStyles : LightStyles;
  return (
    <Tab.Navigator
    screenOptions={({ route }) => ({
        headerShown: false,
        tabBarStyle: styles.container,
        tabBarActiveTintColor: PrimaryColors.SELECTEDTABCOLOR,
        tabBarInactiveTintColor: styles.inActiveIcon.color,
        tabBarIcon: ({ color, size }) => {
          let iconName;
          if (route.name === strings.Home.HOME) {
            iconName = 'home';
          }
          else if (route.name === strings.Home.PROFILE)
          {
            iconName = 'person';
          }
           else if (route.name === strings.Home.SETTING) {
            iconName = 'settings';
          }
          return <Ionicons name={iconName} color={color} size={30} />;
        },
      })}>
    <Tab.Screen name={strings.Home.HOME} component={TutorHome}
    />
     <Tab.Screen name={strings.Home.PROFILE} component={TutorProfile}
    />
    <Tab.Screen name={strings.Home.SETTING} component={TutorSetting}
    />
       
  </Tab.Navigator>
  )
}

export default TabNavigator

const DarkStyles = StyleSheet.create({
  container:{
    backgroundColor:PrimaryColors.BLACK,
    borderTopWidth: 2,
    borderTopColor: PrimaryColors.WHITE,
  },
  inActiveIcon:{
    color:PrimaryColors.WHITE
  }


})

const LightStyles = StyleSheet.create({
  container:{
    backgroundColor:PrimaryColors.WHITE,
      borderTopWidth: 2,
 borderTopColor: PrimaryColors.BLACK,
    
  },
  inActiveIcon:{
    color:PrimaryColors.BLACK
  }
})