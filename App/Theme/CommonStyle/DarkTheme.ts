import {StyleSheet} from 'react-native';
import {PrimaryColors} from '../../Utils/Constants';
export const DarkTheme = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: PrimaryColors.LIGHTGRAY,
    alignItems: 'center',
  },
  placeholderTextColor: {
    color: PrimaryColors.WHITE,
  },
  title: {
    color: PrimaryColors.WHITE,
    fontSize: 24,
    fontWeight: 'bold',
  },
  backgroundShadow: {
    backgroundColor: PrimaryColors.CARDBACKGROUNDDARK,
    shadowColor: PrimaryColors.GRAYSHADOW,
    shadowOffset: {width: 0, height: 4},
    shadowOpacity: 0.25,
    shadowRadius: 6,
    elevation: 10,
    borderWidth: 1,
    borderColor: PrimaryColors.LIGHTGRAY,
  },
  lightBackgroundShadow: {
    backgroundColor: PrimaryColors.CARDBACKGROUNDDARK,
    // shadowColor: PrimaryColors.GRAYSHADOW,
    // shadowOffset: {width: 0, height: 4},
    // shadowOpacity: 0.25,
    // shadowRadius: 6,
    // elevation: 5,
  //  borderColor: isDarkMode ? '#3E3E3E' : '#CCCCCC',
    borderWidth:1
  },
  mainView: {
    backgroundColor: PrimaryColors.BLACK,
  },
  searchInput: {
    flex: 1,
    height: 40,
    fontWeight: 'bold',
    color: PrimaryColors.WHITE,
  },
  filterIcon: {
    borderRadius: '50%',
    height: 40,
    width: 40,
    alignItems: 'center',
    justifyContent: 'center',
  },
  searchIcon: {
    marginRight: 8,
    color: PrimaryColors.WHITE,
  },
  itemContinerText: {
    color: PrimaryColors.WHITE,
  },
  itemContainerBtn: {
    borderColor: PrimaryColors.WHITE,
  },
  profileIconBorder: {
    borderColor: PrimaryColors.LIGHTGRAY2,
  },
  mainTextColor: {
    color: PrimaryColors.WHITE,
  },
  label: {
    color: PrimaryColors.WHITE,
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 6,
  },
  label1: {
    color: '#8D8D8D',
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 6,
  },
  input: {
    borderWidth: 0.5,
    borderColor: PrimaryColors.INPUTFILEDBORDER,
    borderRadius: 8,
    padding: 10,
    color: PrimaryColors.WHITE,
  },
  error: {
    color: PrimaryColors.ERROR,
    fontSize: 14,
    marginTop: 4,
  },
  gridItem: {
    aspectRatio: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  gridImage: {
    width: 80,
    height: 70,
    marginBottom: '5%',
  },
  gridText: {
    fontSize: 14,
    fontWeight: '600',
    color: PrimaryColors.WHITE,
    textAlign: 'center',
  },
  gridContainer: {
    flex: 1,
    backgroundColor: PrimaryColors.LIGHTGRAY,
  },
  profileTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: '2%',
    color: PrimaryColors.WHITE,
  },
  subtitle: {
    fontSize: 14,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: '1%',
    color: PrimaryColors.WHITE,
  },
  description: {
    fontSize: 14,
    textAlign: 'center',
    color: PrimaryColors.WHITE,
    lineHeight: 22,
  },
  prolfileHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 14,
    backgroundColor: PrimaryColors.DARK_HEADER_BG,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: PrimaryColors.DARK_BORDER,
    shadowColor: PrimaryColors.BLACK,
    shadowOffset: {width: 0, height: 1},
    shadowOpacity: 0.1,
    shadowRadius: 3,
    elevation: 2,
  },
  prolfileHeaderText: {
    fontSize: 18,
    fontWeight: '600',
    color: PrimaryColors.WHITE,
  },
  prolfileToggleIcon: {
    fontSize: 20,
    fontWeight: '600',
    color: PrimaryColors.WHITE,
  },
  prolfileContent: {
    paddingVertical: 8,
    paddingHorizontal: 12,
    marginTop: 6,
  },
  prolfileItemCard: {
    backgroundColor: PrimaryColors.DARK_ITEM_BG,
    borderRadius: 8,
    padding: 14,
    marginVertical: 4,
    borderWidth: 1,
    borderColor: PrimaryColors.DARK_ITEM_BORDER,
    shadowColor: PrimaryColors.BLACK,
    shadowOffset: {width: 0, height: 1},
    shadowOpacity: 0.08,
    shadowRadius: 2,
    elevation: 1,
  },
  prolfileItem: {
    fontSize: 15,
    color: PrimaryColors.WHITE,
    marginVertical: 2,
    lineHeight: 22,
  },
  settingItemText: {
    fontSize: 18,
  },
  settingContainer: {
    flex: 1,
    backgroundColor: PrimaryColors.BLACK,
    paddingHorizontal: 16,
    paddingTop: 20,
  },
  ExamTitle: {
    fontSize: 22,
    alignSelf: 'center',
    fontWeight: '800',
    marginTop: 12,
  },
  ExamCardContainer: {
    borderRadius: 16,
    marginBottom: 30,
    marginTop: 2,
  },
  PrizeText: {
    fontSize: 22,
    alignSelf: 'center',
    fontWeight: '400',
  },
  modalView: {
    backgroundColor: PrimaryColors.LIGHTGRAY,
    borderRadius: 20,
    padding: 24,
    width: '95%',
    shadowColor: PrimaryColors.WHITE,
    shadowOffset: {width: 0, height: 4},
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 12,
  },
  modelPickerContainer: {
    borderRadius: 12,
    overflow: 'hidden',
    backgroundColor: PrimaryColors.BLACK,
  },
  modelPickerItem: {
    fontSize: 15,
    color: PrimaryColors.WHITE,
    backgroundColor: PrimaryColors.BLACK,
  },
  ExamCardKey: {
    fontSize: 16,
    alignSelf: 'center',
    fontWeight: '400',
    marginLeft: 4,
    color: PrimaryColors.WHITE,
    marginTop: 2,
  },
  CardValue: {
    fontSize: 16,
    alignSelf: 'center',
    fontWeight: '800',
    marginLeft: 4,
    color: PrimaryColors.WHITE,
  },
  removeButton: {
    height: 50,
    justifyContent: 'center',
    borderRadius: 5,
    width: '20%',
    borderWidth: 1,
    backgroundColor: PrimaryColors.BLACK,
  },
  tutionClassCard: {
    backgroundColor: PrimaryColors.BLACK,
    borderRadius: 20,
    borderColor: PrimaryColors.WHITE,
    padding: 15,
    marginBottom: 10,
    marginTop: 10,
    shadowColor: PrimaryColors.BLACK,
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.1,
    shadowRadius: 20,
    elevation: 2,
  },
  tutionClassCardItem: {
    color: PrimaryColors.WHITE,
    marginBottom: 5,
  },
  testimonialCard: {
    backgroundColor: PrimaryColors.BLACK,
    width: '100%',
    paddingLeft:15,paddingRight:15,
    paddingTop: 15,
    marginTop: 10,
    borderBottomWidth: 0,
    overflow:'hidden',
  },
});
