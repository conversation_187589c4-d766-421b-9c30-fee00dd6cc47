import {StyleSheet} from 'react-native';
import {PrimaryColors} from '../../Utils/Constants';

export const LightTheme = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: PrimaryColors.WHITE,
    alignItems: 'center',
  },
  placeholderTextColor: {
    color: PrimaryColors.WHITE,
  },
  title: {
    color: PrimaryColors.WHITE,
    fontSize: 24,
    fontWeight: 'bold',
  },
  backgroundShadow: {
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 4},
    shadowOpacity: 0.5,
    shadowRadius: 6,
    elevation: 12,
    borderWidth: 1,
    borderColor: '#ddd',
    backgroundColor: PrimaryColors.WHITE,
  },
  lightBackgroundShadow: {
    backgroundColor: PrimaryColors.WHITE,
    borderWidth: 1,
    borderColor: '#ddd',
    shadowColor: '#000',
    shadowOffset: {width: 5, height: 3},
    shadowOpacity: 0.1,
    shadowRadius: 6,
    elevation: 4,
  },
  mainView: {
    backgroundColor: PrimaryColors.WHITE,
  },
  itemContainer: {
    backgroundColor: 'black',
  },
  searchInput: {
    flex: 1,
    height: 40,
    fontWeight: 'bold',
    color: PrimaryColors.BLACK,
  },
  filterIcon: {
    borderRadius: '50%',
    height: 40,
    width: 40,
    alignItems: 'center',
    justifyContent: 'center',
  },
  searchIcon: {
    marginRight: 8,
    color: PrimaryColors.BLACK,
  },
  itemContinerText: {
    color: PrimaryColors.BLACK,
  },
  itemContainerBtn: {
    borderColor: PrimaryColors.BLACK,
  },
  profileIconBorder: {
    borderColor: PrimaryColors.BLACK,
  },
  mainTextColor: {
    color: PrimaryColors.BLACK,
  },
  label: {
    color: PrimaryColors.BLACK,
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 6,
  },
  label1: {
    color: '#6E6E6E',
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 6,
  },
  input: {
    borderWidth: 0.5,
    borderRadius: 8,
    padding: 10,
    color: PrimaryColors.BLACK,
  },
  button: {
    backgroundColor: PrimaryColors.ORANGE,
    width: '80%',
    padding: 15,
    borderRadius: 8,
    marginTop: 20,
    alignItems: 'center',
  },
  error: {
    color: PrimaryColors.ERROR,
    fontSize: 14,
    marginTop: 4,
  },
  gridContainer: {
    flex: 1,
    backgroundColor: PrimaryColors.WHITE,
  },
  gridItem: {
    aspectRatio: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  gridImage: {
    width: 80,
    height: 70,
    marginBottom: '5%',
  },
  gridText: {
    fontSize: 14,
    fontWeight: '600',
    color: PrimaryColors.BLACK,
    textAlign: 'center',
  },
  profileTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: '2%',
    color: PrimaryColors.BLACK,
  },
  subtitle: {
    fontSize: 14,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: '1%',
    color: PrimaryColors.BLACK,
  },
  description: {
    fontSize: 14,
    textAlign: 'center',
    color: PrimaryColors.BLACK,
    lineHeight: 22,
  },
  prolfileHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 14,
    backgroundColor: PrimaryColors.LIGHTORANGE,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: PrimaryColors.LIGHT_BORDER,
    shadowColor: PrimaryColors.BLACK,
    shadowOffset: {width: 0, height: 1},
    shadowOpacity: 0.08,
    shadowRadius: 3,
    elevation: 2,
  },
  prolfileHeaderText: {
    fontSize: 18,
    fontWeight: '600',
    color: PrimaryColors.BLACK,
  },
  prolfileToggleIcon: {
    fontSize: 20,
    fontWeight: '600',
    color: PrimaryColors.BLACK,
  },
  prolfileContent: {
    paddingVertical: 8,
    paddingHorizontal: 12,
    marginTop: 6,
  },
  prolfileItemCard: {
    backgroundColor: PrimaryColors.WHITE,
    borderRadius: 8,
    padding: 14,
    marginVertical: 4,
    borderWidth: 1,
    borderColor: PrimaryColors.LIGHT_ITEM_BORDER,
    shadowColor: PrimaryColors.BLACK,
    shadowOffset: {width: 0, height: 1},
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  prolfileItem: {
    fontSize: 15,
    color: PrimaryColors.BLACK,
    marginVertical: 2,
    lineHeight: 22,
  },
  settingItemText: {
    fontSize: 18,
  },
  settingContainer: {
    flex: 1,
    backgroundColor: PrimaryColors.WHITE,
    paddingHorizontal: 16,
    paddingTop: 20,
  },
  ExamTitle: {
    fontSize: 22,
    alignSelf: 'center',
    fontWeight: '800',
    marginTop: 12,
  },
  ExamCardContainer: {
    borderRadius: 16,
    marginBottom: 30,
    marginTop: 2,
  },
  PrizeText: {
    fontSize: 22,
    alignSelf: 'center',
    fontWeight: '400',
  },
  modalView: {
    backgroundColor: PrimaryColors.WHITE,
    borderRadius: 20,
    padding: 24,
    width: '95%',
    shadowColor: PrimaryColors.LIGHTGRAY,
    shadowOffset: {width: 0, height: 4},
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 12,
  },
  modelPickerContainer: {
    borderWidth: 1,
    borderRadius: 12,
    overflow: 'hidden',
  },
  modelPickerItem: {
    fontSize: 15,
    color: PrimaryColors.BLACK,
    backgroundColor: PrimaryColors.WHITE,
  },
  ExamCardKey: {
    fontSize: 16,
    alignSelf: 'center',
    fontWeight: '400',
    marginLeft: 4,
    marginTop: 2,
  },
  CardValue: {
    fontSize: 16,
    alignSelf: 'center',
    fontWeight: '800',
    marginLeft: 4,
  },
  removeButton: {
    height: 50,
    justifyContent: 'center',
    borderRadius: 5,
    width: '20%',
    borderWidth: 1,
  },
  tutionClassCard: {
    backgroundColor: PrimaryColors.LIGHTGRAY2,
    borderRadius: 20,
    borderColor: PrimaryColors.BLACK,
    padding: 15,
    marginBottom: 10,
    marginTop: 10,
    shadowColor: PrimaryColors.BLACK,
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.1,
    shadowRadius: 20,
    elevation: 2,
  },
  tutionClassCardItem: {
    color: PrimaryColors.BLACK,
    marginBottom: 5,
  },
  testimonialCard: {
    backgroundColor: PrimaryColors.WHITE,
    width: '100%',
    borderTopLeftRadius: 26,
    borderTopRightRadius: 26,
    padding: 15,
    marginTop: 10,
    borderBottomWidth: 0,
  },
});
