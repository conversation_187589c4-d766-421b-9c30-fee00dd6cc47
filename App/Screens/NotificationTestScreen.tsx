import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  ScrollView,
  Alert,
  SafeAreaView,
} from 'react-native';
import NotificationTester from '../utils/NotificationTester';
import FirebaseNotificationService from '../NotificationHelper/FirebaseNotificationService';
import FCMTokenService from '../services/fcmTokenService';

const NotificationTestScreen = () => {
  const [testResults, setTestResults] = useState({});
  const [isServiceInitialized, setIsServiceInitialized] = useState(false);
  const [currentToken, setCurrentToken] = useState(null);

  useEffect(() => {
    checkServiceStatus();
  }, []);

  const checkServiceStatus = async () => {
    const initialized = FirebaseNotificationService.isServiceInitialized();
    setIsServiceInitialized(initialized);
    
    if (initialized) {
      const token = await FCMTokenService.getCurrentToken();
      setCurrentToken(token ? token.substring(0, 30) + '...' : null);
    }
  };

  const runTest = async (testName, testFunction) => {
    try {
      setTestResults(prev => ({ ...prev, [testName]: 'running' }));
      const result = await testFunction();
      setTestResults(prev => ({ ...prev, [testName]: result ? 'success' : 'failed' }));
      await checkServiceStatus();
    } catch (error) {
      setTestResults(prev => ({ ...prev, [testName]: 'error' }));
      console.error(`Test ${testName} failed:`, error);
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'success': return '#4CAF50';
      case 'failed': return '#F44336';
      case 'error': return '#FF9800';
      case 'running': return '#2196F3';
      default: return '#9E9E9E';
    }
  };

  const getStatusText = (status) => {
    switch (status) {
      case 'success': return '✅ PASS';
      case 'failed': return '❌ FAIL';
      case 'error': return '⚠️ ERROR';
      case 'running': return '🔄 RUNNING';
      default: return '⏳ PENDING';
    }
  };

  const TestButton = ({ title, onPress, status }) => (
    <TouchableOpacity
      style={[styles.testButton, { borderColor: getStatusColor(status) }]}
      onPress={onPress}
      disabled={status === 'running'}
    >
      <Text style={styles.testButtonText}>{title}</Text>
      <Text style={[styles.statusText, { color: getStatusColor(status) }]}>
        {getStatusText(status)}
      </Text>
    </TouchableOpacity>
  );

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.scrollView}>
        <Text style={styles.title}>🧪 Push Notification Tests</Text>
        
        {/* Service Status */}
        <View style={styles.statusContainer}>
          <Text style={styles.statusTitle}>Service Status</Text>
          <Text style={[styles.statusValue, { color: isServiceInitialized ? '#4CAF50' : '#F44336' }]}>
            {isServiceInitialized ? '✅ Initialized' : '❌ Not Initialized'}
          </Text>
          {currentToken && (
            <Text style={styles.tokenText}>Token: {currentToken}</Text>
          )}
        </View>

        {/* Individual Tests */}
        <View style={styles.testsContainer}>
          <Text style={styles.sectionTitle}>Individual Tests</Text>
          
          <TestButton
            title="Test Permissions"
            status={testResults.permissions}
            onPress={() => runTest('permissions', NotificationTester.testPermissions)}
          />
          
          <TestButton
            title="Test Service Init"
            status={testResults.service}
            onPress={() => runTest('service', NotificationTester.testServiceInitialization)}
          />
          
          <TestButton
            title="Test Token Generation"
            status={testResults.token}
            onPress={() => runTest('token', NotificationTester.testTokenGeneration)}
          />
          
          <TestButton
            title="Test Local Notification"
            status={testResults.localNotification}
            onPress={() => runTest('localNotification', NotificationTester.testLocalNotification)}
          />
          
          <TestButton
            title="Test Token Refresh"
            status={testResults.tokenRefresh}
            onPress={() => runTest('tokenRefresh', NotificationTester.testTokenRefresh)}
          />
        </View>

        {/* Batch Tests */}
        <View style={styles.testsContainer}>
          <Text style={styles.sectionTitle}>Batch Tests</Text>
          
          <TouchableOpacity
            style={[styles.batchButton, styles.primaryButton]}
            onPress={() => NotificationTester.runAllTests()}
          >
            <Text style={styles.batchButtonText}>🚀 Run All Tests</Text>
          </TouchableOpacity>
          
          <TouchableOpacity
            style={[styles.batchButton, styles.secondaryButton]}
            onPress={() => NotificationTester.testNotificationTypes()}
          >
            <Text style={styles.batchButtonText}>📱 Test Notification Types</Text>
          </TouchableOpacity>
        </View>

        {/* Manual Actions */}
        <View style={styles.testsContainer}>
          <Text style={styles.sectionTitle}>Manual Actions</Text>
          
          <TouchableOpacity
            style={styles.actionButton}
            onPress={async () => {
              const tokenInfo = await FCMTokenService.getTokenInfo();
              Alert.alert('Token Info', JSON.stringify(tokenInfo, null, 2));
            }}
          >
            <Text style={styles.actionButtonText}>📋 Show Token Info</Text>
          </TouchableOpacity>
          
          <TouchableOpacity
            style={styles.actionButton}
            onPress={async () => {
              const success = await FirebaseNotificationService.syncTokenAfterLogin();
              Alert.alert('Sync Result', success ? 'Success ✅' : 'Failed ❌');
            }}
          >
            <Text style={styles.actionButtonText}>🔄 Sync Token with Server</Text>
          </TouchableOpacity>
          
          <TouchableOpacity
            style={styles.actionButton}
            onPress={() => {
              setTestResults({});
              setIsServiceInitialized(false);
              setCurrentToken(null);
              Alert.alert('Reset Complete', 'Test results cleared');
            }}
          >
            <Text style={styles.actionButtonText}>🗑️ Clear Test Results</Text>
          </TouchableOpacity>
        </View>

        <View style={styles.footer}>
          <Text style={styles.footerText}>
            Use this screen to test push notification functionality.{'\n'}
            Make sure to test in different app states: foreground, background, and killed.
          </Text>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  scrollView: {
    flex: 1,
    padding: 16,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 20,
    color: '#333',
  },
  statusContainer: {
    backgroundColor: 'white',
    padding: 16,
    borderRadius: 8,
    marginBottom: 20,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  statusTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 8,
    color: '#333',
  },
  statusValue: {
    fontSize: 14,
    fontWeight: '600',
  },
  tokenText: {
    fontSize: 12,
    color: '#666',
    marginTop: 4,
    fontFamily: 'monospace',
  },
  testsContainer: {
    marginBottom: 20,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 12,
    color: '#333',
  },
  testButton: {
    backgroundColor: 'white',
    padding: 16,
    borderRadius: 8,
    marginBottom: 8,
    borderWidth: 2,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    elevation: 1,
  },
  testButtonText: {
    fontSize: 16,
    color: '#333',
    flex: 1,
  },
  statusText: {
    fontSize: 14,
    fontWeight: 'bold',
  },
  batchButton: {
    padding: 16,
    borderRadius: 8,
    marginBottom: 8,
    alignItems: 'center',
  },
  primaryButton: {
    backgroundColor: '#FD904B',
  },
  secondaryButton: {
    backgroundColor: '#2196F3',
  },
  batchButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
  actionButton: {
    backgroundColor: 'white',
    padding: 12,
    borderRadius: 8,
    marginBottom: 8,
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#ddd',
  },
  actionButtonText: {
    color: '#333',
    fontSize: 14,
  },
  footer: {
    marginTop: 20,
    padding: 16,
    backgroundColor: '#e3f2fd',
    borderRadius: 8,
  },
  footerText: {
    fontSize: 12,
    color: '#1976d2',
    textAlign: 'center',
    lineHeight: 18,
  },
});

export default NotificationTestScreen;
