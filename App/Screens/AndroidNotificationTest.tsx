import React from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  ScrollView,
  SafeAreaView,
  Platform,
  Alert,
} from 'react-native';
import AndroidNotificationTester from '../utils/AndroidNotificationTester';

const AndroidNotificationTest = () => {

  if (Platform.OS !== 'android') {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>
            ❌ This screen is for Android testing only.
          </Text>
        </View>
      </SafeAreaView>
    );
  }

  const TestButton = ({ title, onPress, description, emoji }) => (
    <TouchableOpacity style={styles.testButton} onPress={onPress}>
      <View style={styles.buttonContent}>
        <Text style={styles.buttonEmoji}>{emoji}</Text>
        <View style={styles.buttonTextContainer}>
          <Text style={styles.buttonTitle}>{title}</Text>
          <Text style={styles.buttonDescription}>{description}</Text>
        </View>
      </View>
    </TouchableOpacity>
  );

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.scrollView}>
        <Text style={styles.title}>🤖 Android Push Notification Tests</Text>
        
        <View style={styles.infoContainer}>
          <Text style={styles.infoText}>
            Android Version: {Platform.Version}{'\n'}
            POST_NOTIFICATIONS Required: {Platform.Version >= 33 ? 'Yes' : 'No'}
          </Text>
        </View>

        <View style={styles.testsContainer}>
          <Text style={styles.sectionTitle}>Individual Tests</Text>
          
          <TestButton
            emoji="📱"
            title="Test Permissions"
            description="Check if notification permissions are granted"
            onPress={() => AndroidNotificationTester.testPermissions()}
          />
          
          <TestButton
            emoji="🔑"
            title="Test FCM Token"
            description="Generate and display FCM token for testing"
            onPress={() => AndroidNotificationTester.testFCMToken()}
          />
          
          <TestButton
            emoji="📨"
            title="Test Notification Display"
            description="Simulate how notifications will appear"
            onPress={() => AndroidNotificationTester.testNotificationDisplay()}
          />
          
          <TestButton
            emoji="⚙️"
            title="Check Settings"
            description="Review all notification settings"
            onPress={() => AndroidNotificationTester.checkNotificationSettings()}
          />
          
          <TestButton
            emoji="📤"
            title="Prepare Test Notification"
            description="Generate payload for manual testing"
            onPress={() => AndroidNotificationTester.sendTestNotification()}
          />
        </View>

        <View style={styles.batchContainer}>
          <Text style={styles.sectionTitle}>Batch Tests</Text>
          
          <TouchableOpacity
            style={[styles.batchButton, styles.primaryButton]}
            onPress={() => AndroidNotificationTester.runAllAndroidTests()}
          >
            <Text style={styles.batchButtonText}>🚀 Run All Tests</Text>
          </TouchableOpacity>
        </View>

        <View style={styles.instructionsContainer}>
          <Text style={styles.instructionsTitle}>📋 Testing Instructions</Text>
          <Text style={styles.instructionsText}>
            1. Run "Test Permissions" first{'\n'}
            2. Run "Test FCM Token" to get your token{'\n'}
            3. Use "Prepare Test Notification" to get payload{'\n'}
            4. Send notification using Firebase Console or Postman{'\n'}
            5. Test in different app states: foreground, background, killed
          </Text>
        </View>

        <View style={styles.troubleshootContainer}>
          <Text style={styles.troubleshootTitle}>🔧 Troubleshooting</Text>
          <Text style={styles.troubleshootText}>
            • If no notifications appear, check device notification settings{'\n'}
            • For Android 13+, ensure POST_NOTIFICATIONS permission is granted{'\n'}
            • Check logcat for detailed error messages{'\n'}
            • Verify Firebase configuration is correct{'\n'}
            • Test with Firebase Console first before using custom backend
          </Text>
        </View>

        <TouchableOpacity
          style={styles.logcatButton}
          onPress={() => {
            Alert.alert(
              'View Logs',
              'To see detailed logs, run:\n\nadb logcat | grep UESTFCMService\n\nOr check React Native debugger console.',
              [{ text: 'OK' }]
            );
          }}
        >
          <Text style={styles.logcatButtonText}>📋 View Logs Instructions</Text>
        </TouchableOpacity>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  scrollView: {
    flex: 1,
    padding: 16,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 20,
    color: '#333',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  errorText: {
    fontSize: 18,
    color: '#F44336',
    textAlign: 'center',
  },
  infoContainer: {
    backgroundColor: '#e3f2fd',
    padding: 12,
    borderRadius: 8,
    marginBottom: 20,
  },
  infoText: {
    fontSize: 14,
    color: '#1976d2',
    textAlign: 'center',
  },
  testsContainer: {
    marginBottom: 20,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 12,
    color: '#333',
  },
  testButton: {
    backgroundColor: 'white',
    padding: 16,
    borderRadius: 8,
    marginBottom: 8,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  buttonContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  buttonEmoji: {
    fontSize: 24,
    marginRight: 12,
  },
  buttonTextContainer: {
    flex: 1,
  },
  buttonTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 4,
  },
  buttonDescription: {
    fontSize: 14,
    color: '#666',
  },
  batchContainer: {
    marginBottom: 20,
  },
  batchButton: {
    padding: 16,
    borderRadius: 8,
    alignItems: 'center',
  },
  primaryButton: {
    backgroundColor: '#FD904B',
  },
  batchButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
  instructionsContainer: {
    backgroundColor: '#fff3e0',
    padding: 16,
    borderRadius: 8,
    marginBottom: 16,
  },
  instructionsTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#f57c00',
    marginBottom: 8,
  },
  instructionsText: {
    fontSize: 14,
    color: '#ef6c00',
    lineHeight: 20,
  },
  troubleshootContainer: {
    backgroundColor: '#ffebee',
    padding: 16,
    borderRadius: 8,
    marginBottom: 16,
  },
  troubleshootTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#d32f2f',
    marginBottom: 8,
  },
  troubleshootText: {
    fontSize: 14,
    color: '#c62828',
    lineHeight: 20,
  },
  logcatButton: {
    backgroundColor: '#424242',
    padding: 12,
    borderRadius: 8,
    alignItems: 'center',
    marginBottom: 20,
  },
  logcatButtonText: {
    color: 'white',
    fontSize: 14,
    fontWeight: 'bold',
  },
});

export default AndroidNotificationTest;
