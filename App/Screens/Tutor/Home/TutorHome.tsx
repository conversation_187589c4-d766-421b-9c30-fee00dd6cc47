// App/Screens/Login/Login.tsx
import React from 'react';
import { StyleSheet, Text, TouchableOpacity, Image, View } from 'react-native';
import { SafeAreaView, SafeAreaProvider } from 'react-native-safe-area-context';
import { IMAGE_CONSTANT } from '../../../Utils/Constants';
import strings from '../../../Utils/LocalizedStrings/LocalizedStrings';
import { useNavigation } from '@react-navigation/native';
import Header from '../../../CommonComponents/Header';
import IndexStyle from '../../../Theme/IndexStyle';

const TutorHome = () => {
  const navigation = useNavigation<any>();
  const { styles, isDarkMode } = IndexStyle();


  const gridItems = [
    // {
    //   title: strings.Home.FINDCLASS,
    //   Image: IMAGE_CONSTANT.FINDCLASSES,
    //   Action: 'ClassList',
    // },
    {
      title: strings.Home.UWHIZQUIZ,
      Image: IMAGE_CONSTANT.EXAMLOGO,
      Action: 'Exam',
    },
    // {
    //   title: "Create Tution",
    //   Image: IMAGE_CONSTANT.EXAMLOGO,
    //   Action: "CreateTution"
    // },
    {
      title: "Blog",
      Image: IMAGE_CONSTANT.EXAMLOGO,
      Action: "Blog"
    },
    {
      title: "Testimonials",
      Image: IMAGE_CONSTANT.EXAMLOGO,
      Action: "Testmonials"
    }, {
      title: "Thoughts",
      Image: IMAGE_CONSTANT.EXAMLOGO,
      Action: "Thoughts"
    },
    // …add more items here…
  ];

  return (
    <SafeAreaProvider style={styles.gridContainer}>
      <Header title={strings.Home.DASHBOARD} />
      <SafeAreaView style={BaseStyle.content}>
        <View style={BaseStyle.homeGrid}>
          {gridItems.map((item, index) => (
            <View key={index} style={[BaseStyle.gridWrapper, styles.backgroundShadow]}>
              <TouchableOpacity
                onPress={() => navigation.navigate(item.Action)}
                style={styles.gridItem}>
                <Image
                  resizeMode="contain"
                  source={item.Image}
                  style={styles.gridImage}
                />
                <Text style={styles.gridText}>{item.title}</Text>
              </TouchableOpacity>
            </View>
          ))}
        </View>
      </SafeAreaView>
    </SafeAreaProvider>
  );
};

export default TutorHome;

const BaseStyle = StyleSheet.create({
  content: {
    flex: 1,
    paddingHorizontal: 20,
    paddingTop: 30,
  },
  homeGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-around',
  },
  gridWrapper: {
    width: '30%',
    marginBottom: 20,
    borderRadius: 20,
  },
});
