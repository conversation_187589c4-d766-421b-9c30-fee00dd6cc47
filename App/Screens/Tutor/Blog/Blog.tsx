import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Modal,
  Image,
  useWindowDimensions,
} from 'react-native';
import { PrimaryColors } from '../../../Utils/Constants';
import { useNavigation } from '@react-navigation/native';
import { SafeAreaView, SafeAreaProvider } from 'react-native-safe-area-context';
import Header from '../../../CommonComponents/Header';
import CommonFileUpload from '../../../CommonComponents/CommonFileUpload';
import DocumentPicker from '@react-native-documents/picker';
import CommonTextInput from '../../../CommonComponents/CommonTextInput';
import strings from '../../../Utils/LocalizedStrings/LocalizedStrings';
import IndexStyle from '../../../Theme/IndexStyle';
import { FlatList, ScrollView } from 'react-native-gesture-handler';
import type { DocumentPickerResponse } from '@react-native-documents/picker';
import api from '../../../config/api';
import { imgBaseUrl } from '../../../config/apiUrl';
import RenderHTML from 'react-native-render-html';
import Toast from 'react-native-simple-toast';



const Blog = () => {
  type BlogItem = {
    id: string;
    blogTitle: string;
    blogImage: string;
    blogdescription: string;
  };
  const navigation = useNavigation();
  const { styles, isDarkMode } = IndexStyle();

  const [modalVisible, setModalVisible] = useState(false);
  const [title, setTitle] = useState('');
  const [description, setDescription] = useState('');
  const [proofFile, setProofFile] =
    useState<DocumentPicker.DocumentPickerResponse | null>(null);
  const [blogList, setBlogList] = useState<BlogItem[]>([]);
  const [classId, setClassId] = useState<string | null>(null);
  const [editingId, setEditingId] = useState<string | null>(null);
  const [formSubmitted, setFormSubmitted] = useState(false);
  const [blog, setBlog] = useState<any[]>([]);
  const { width } = useWindowDimensions();

  React.useEffect(() => {
    fetchBlogData();
  }, []);

  const fetchBlogData = async (pageNumber = 1) => {
    try {
      // const classId = await AsyncStorage.getItem('classId');

      const response = await api.GetBlogData.getBlogData(pageNumber, 10);

      if (response.ok) {
        const text = await response.text();
        const jsonData = JSON.parse(text);
        setBlog(jsonData.blogs || []);
        console.log("PARSED  DATA::", jsonData);
        console.log("Review  DATA::", jsonData.blogs);
      } else {
        console.log("Failed to Blog data. Status:", response.status);
      }
    } catch (err) {
      console.log("ERROR IN GET BLOG DATA::", err);
    }
  };

  const addOrUpdateBlog = async () => {
    setFormSubmitted(true);

    if (!title.trim() || !description.trim() || !proofFile) return;

    const extension = proofFile.name?.substring(proofFile.name.lastIndexOf(".")) || ".jpg";
    const nameWithoutExt = proofFile.name?.replace(extension, "") || "image";
    const timestamp = Date.now();
    const randomNum = Math.floor(Math.random() * 1000000000);
    const uniqueName = `${nameWithoutExt}-${timestamp}-${randomNum}${extension}`;

    try {
      if (editingId) {
        const updateData = {
          blogTitle: title,
          blogDescription: description,
          // blogImage: `$uploads/classes/efd64e13-b4ec-4a28-9805-08a6b80c2042/blogs/${proofFile.name}`,
          blogImage: `uploads/classes/${classId}/blogs/${uniqueName}`,
        };
        console.log("Updating blog with:", updateData);

        const response = await api.EditBlogData.editBlogData(editingId, updateData);
        console.log("class ID", classId);

        if (response.ok) {
          Toast.show("Blog updated successfully", Toast.SHORT);
        } else {
          Toast.show("Failed to update blog", Toast.LONG);
        }
      } else {
        if (!proofFile) {
          Toast.show("Please upload an image", Toast.SHORT);
          return;
        }

        const formData = new FormData();
        formData.append('blogTitle', title);
        formData.append('blogDescription', description);
        formData.append('blogImage', {
          uri: proofFile.uri,
          name: proofFile.name || 'image.jpg',
          type: proofFile.type || 'image/jpeg',
        });

        const addData = {
          blogTitle: title,
          blogDescription: description,
          // blogImage: `uploads/classes/${editingId}/blogs/${proofFile.name}`,
          blogImage: `uploads/classes/${classId}/blogs/${uniqueName}`,
        };

        console.log("Adding blog with:", addData);

        const response = await api.AddBlogData.addBlogData(addData);
        console.log("RESPONSE :::::::::", response);

        if (response.ok) {
          Toast.show("Blog created successfully", Toast.SHORT);
          console.log("Blog Response:", response);

        } else {
          Toast.show("Failed to create blog", Toast.LONG);
        }
        console.log("ADD DATA RESPONSE :::::::::", addData);
        console.log("RESPONSE :::::::::", response);
      }

      // Reset form state
      setTitle('');
      setDescription('');
      setProofFile(null);
      setEditingId(null);
      setFormSubmitted(false);
      setModalVisible(false);
      fetchBlogData(); // Refresh blog list
    } catch (err) {
      console.log("API Error:", err);
      Toast.show("Something went wrong", Toast.LONG);
    }
  };


  const deleteBlog = (id: string) => {
    setBlogList(prev => prev.filter(item => item.id !== id));
  };

  const handleEdit = (item: any) => {
    setTitle(item.blogTitle || '');
    setDescription(item.blogDescription || '');
    setProofFile({
      // uri: `${imgBaseUrl}/${item.blogImage}`,
      uri: `uploads/classes/${item.classId}/blogs/${item.blogImage}`,
      name: item.blogImage?.split('/').pop() || 'image.jpg',
      type: 'image/jpeg',
    } as DocumentPickerResponse);
    console.log('Profile DATA ::::: ', item.blogImage);
    console.log('Class Id :::: ', item.classId);

    setEditingId(item.id);
    setClassId(item.classId);
    setModalVisible(true);
  };

  return (
    <SafeAreaProvider style={styles.container}>
      <Header title={strings.Blog.BLOG} />

      <SafeAreaView style={{ width: '100%', paddingHorizontal: '5%' }}>
        <ScrollView style={{ marginBottom: '30%' }}>
          <View style={{ alignItems: 'center', marginBottom: '5%' }}>
            <TouchableOpacity
              style={{
                borderRadius: 10,
                height: 50,
                justifyContent: 'center',
                alignItems: 'center',
                width: '35%',
                borderWidth: 1,
                borderColor: isDarkMode
                  ? PrimaryColors.WHITE
                  : PrimaryColors.BLACK,
              }}
              onPress={() => {
                setModalVisible(true);
                setTitle('');
                setDescription('');
                setProofFile(null);
                setEditingId(null);
                setClassId(classId);
              }}>
              <Text
                style={{
                  color: isDarkMode ? PrimaryColors.WHITE : PrimaryColors.BLACK,
                  fontWeight: '600',
                  fontSize: 16,
                }}>
                ➕ {strings.Blog.ADDBLOG}
              </Text>
            </TouchableOpacity>
          </View>
          <FlatList
            data={blog}
            keyExtractor={item => item.id}
            renderItem={({ item }) => (
              <View
                style={{
                  backgroundColor: isDarkMode ? '#1c1c1e' : '#fff',
                  borderRadius: 12,
                  marginHorizontal: 15,
                  marginBottom: 20,
                  padding: 16,
                  shadowColor: '#000',
                  shadowOffset: { width: 0, height: 2 },
                  shadowOpacity: 0.1,
                  shadowRadius: 4,
                  elevation: 3,
                }}
              >
                <Text
                  style={{
                    fontSize: 18,
                    fontWeight: '700',
                    color: isDarkMode ? PrimaryColors.WHITE : PrimaryColors.BLACK,
                    marginBottom: 8,
                  }}
                >
                  {item.blogTitle}
                </Text>

                {item.blogImage ? (
                  <Image
                    source={{ uri: `${imgBaseUrl}/${item.blogImage}` }}
                    style={{
                      width: '100%',
                      height: 180,
                      borderRadius: 10,
                      marginBottom: 12,
                    }}
                    resizeMode="cover"
                  />
                ) : (
                  <View
                    style={{
                      width: '100%',
                      height: 180,
                      borderRadius: 10,
                      backgroundColor: '#e0e0e0',
                      justifyContent: 'center',
                      alignItems: 'center',
                      marginBottom: 12,
                    }}
                  >
                    <Text style={{ color: '#888' }}>{strings.Blog.NOIMAGE}</Text>
                  </View>
                )}

                <RenderHTML
                  contentWidth={width}
                  source={{ html: item.blogDescription }}
                  baseStyle={{
                    fontSize: 14,
                    color: isDarkMode ? PrimaryColors.WHITE : PrimaryColors.BLACK,
                    marginBottom: 12,
                    lineHeight: 20,
                  }}
                  tagsStyles={{
                    b: { fontWeight: 'bold' },
                    strong: { fontWeight: 'bold' },
                    i: { fontStyle: 'italic' },
                    em: { fontStyle: 'italic' },
                    u: { textDecorationLine: 'underline' },
                    ins: { textDecorationLine: 'underline' },
                    del: { textDecorationLine: 'line-through' },
                    s: { textDecorationLine: 'line-through' },
                    strike: { textDecorationLine: 'line-through' },
                    mark: { backgroundColor: 'yellow' },
                    code: {
                      fontFamily: 'monospace',
                      backgroundColor: '#f4f4f4',
                      paddingHorizontal: 4,
                      borderRadius: 4,
                    },
                    pre: {
                      fontFamily: 'monospace',
                      backgroundColor: '#f4f4f4',
                      padding: 8,
                      borderRadius: 6,
                    },
                    sup: {
                      fontSize: 10,
                      lineHeight: 14,
                      textAlignVertical: 'top',
                    },
                    sub: {
                      fontSize: 10,
                      lineHeight: 14,
                      textAlignVertical: 'bottom',
                    },
                  }}
                />

                <View style={{ flexDirection: 'row', justifyContent: 'space-between', marginBottom: 10 }}>
                  <Text
                    style={{
                      fontSize: 12,
                      color: '#777',
                    }}
                  >
                    {new Date(item.createdAt).toLocaleDateString('en-GB')}
                  </Text>

                  <View
                    style={{
                      backgroundColor:
                        item.status === 'Approved'
                          ? PrimaryColors.GREEN
                          : item.status === 'Pending'
                            ? PrimaryColors.WHITE
                            : PrimaryColors.ORANGE,
                      paddingHorizontal: 10,
                      paddingVertical: 4,
                      borderRadius: 8,
                    }}
                  >
                    <Text
                      style={{
                        color: PrimaryColors.WHITE,
                        fontSize: 12,
                        fontWeight: '600',
                      }}
                    >
                      {item.status}
                    </Text>
                  </View>
                </View>

                <View
                  style={{
                    flexDirection: 'row',
                    justifyContent: 'flex-end',
                    marginTop: 10,
                  }}
                >
                  <TouchableOpacity
                    onPress={() => deleteBlog(item.id)}
                    style={{
                      backgroundColor: PrimaryColors.RED,
                      paddingHorizontal: 12,
                      paddingVertical: 6,
                      borderRadius: 6,
                      marginRight: 10,
                    }}
                  >
                    <Text

                      style={{ color: PrimaryColors.WHITE, fontWeight: '600', fontSize: 14 }}
                    >
                      {strings.Blog.DELETE}
                    </Text>
                  </TouchableOpacity>

                  <TouchableOpacity
                    onPress={() => handleEdit(item)}
                    style={{
                      backgroundColor: PrimaryColors.BLACK,
                      paddingHorizontal: 12,
                      paddingVertical: 6,
                      borderRadius: 6,
                    }}
                  >
                    <Text
                      style={{ color: PrimaryColors.WHITE, fontWeight: '600', fontSize: 14 }}
                    >
                      {strings.Blog.UPDATE}
                    </Text>
                  </TouchableOpacity>
                </View>
              </View>
            )}
            ListEmptyComponent={
              <Text
                style={{
                  textAlign: 'center',
                  color: isDarkMode
                    ? PrimaryColors.LIGHTGRAY2
                    : PrimaryColors.LIGHTGRAY,
                }}>
                {strings.Blog.NOBLOGPOSTFOUND}
              </Text>
            }
          />
        </ScrollView>
      </SafeAreaView>
      <Modal
        visible={modalVisible}
        transparent
        animationType="fade"
        onRequestClose={() => setModalVisible(false)}>
        <View
          style={{
            justifyContent: 'center',
            alignItems: 'center',
            flex: 1,
            margin: 15,
          }}>
          <View style={[styles.modalView, { marginTop: '5%' }]}>
            <Text style={[styles.label, { marginBottom: 10, fontSize: 20 }]}>
              {editingId ? strings.Blog.UPDATE : strings.Blog.CREATEBLOG}
            </Text>

            <CommonTextInput
              label={strings.Blog.TITLE}
              placeholder={strings.Blog.ENTERTITLE}
              value={title}
              onChangeText={setTitle}
              style={{ width: '100%' }}
            />
            {formSubmitted && !title.trim() && (
              <Text
                style={{
                  color: PrimaryColors.RED,
                  fontSize: 12,
                  marginBottom: '5%',
                }}>
                {strings.Blog.TITLE} {strings.ALL.ISREQUIRED}
              </Text>
            )}

            <CommonFileUpload
              label={strings.Blog.IMAGE}
              fileName={proofFile?.name || null}
              onFileSelect={setProofFile}
              placeholder={strings.Blog.UPLOADIMAGE}
              style={{ width: '100%' }}
            />
            {formSubmitted && !proofFile && (
              <Text
                style={{
                  color: PrimaryColors.RED,
                  fontSize: 12,
                  marginBottom: '5%',
                }}>
                {strings.Blog.IMAGE}  {strings.ALL.ISREQUIRED}
              </Text>
            )}

            <CommonTextInput
              label={strings.Blog.DESCRIPTION}
              placeholder={strings.Blog.ENTERDESCRIPTION}
              value={description}
              onChangeText={setDescription}
              multiline={true}
              style={{ width: '100%' }}
            />
            {formSubmitted && !description.trim() && (
              <Text
                style={{
                  color: PrimaryColors.RED,
                  fontSize: 12,
                  marginBottom: '5%',
                }}>
                {strings.Blog.DESCRIPTION} {strings.ALL.ISREQUIRED}
              </Text>
            )}

            <View
              style={{
                flexDirection: 'row',
                justifyContent: 'flex-end',
                marginTop: 10,
              }}>
              <TouchableOpacity
                style={{
                  borderRadius: 10,
                  height: 45,
                  width: '22%',
                  borderWidth: 1,
                  justifyContent: 'center',
                  alignItems: 'center',
                  backgroundColor: isDarkMode
                    ? PrimaryColors.BLACK
                    : PrimaryColors.WHITE,
                  marginRight: 10,
                }}
                onPress={() => setModalVisible(false)}>
                <Text
                  style={{
                    color: isDarkMode
                      ? PrimaryColors.WHITE
                      : PrimaryColors.BLACK,
                    fontWeight: '600',
                  }}>
                  {strings.Blog.CANCEL}
                </Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={{
                  borderRadius: 10,
                  height: 45,
                  width: '22%',
                  borderWidth: 1,
                  justifyContent: 'center',
                  alignItems: 'center',
                  backgroundColor: isDarkMode
                    ? PrimaryColors.WHITE
                    : PrimaryColors.BLACK,
                }}
                onPress={addOrUpdateBlog}>
                <Text
                  style={{
                    color: isDarkMode
                      ? PrimaryColors.BLACK
                      : PrimaryColors.WHITE,
                    fontWeight: '600',
                  }}>
                  {editingId ? strings.Blog.UPDATE : strings.Blog.CREATE}
                </Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>
    </SafeAreaProvider>
  );
};

export default Blog;
