import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Modal,
  TextInput,
  FlatList,
  Alert,
  ActivityIndicator,
} from 'react-native';
import { PrimaryColors } from '../../../Utils/Constants';
import { useNavigation, useRoute } from '@react-navigation/native';
import { SafeAreaView, SafeAreaProvider } from 'react-native-safe-area-context';
import Header from '../../../CommonComponents/Header';
import strings from '../../../Utils/LocalizedStrings/LocalizedStrings';
import IndexStyle from '../../../Theme/IndexStyle';
import api from '../../../config/api';
import { useSelector } from 'react-redux';
import { RootState } from '../../../Redux/store';
import Toast from 'react-native-simple-toast';


const Thoughts = () => {
    const user = useSelector((state: RootState) => state.user);
    // const classId=user.userData?.id   
    const classId = "efd64e13-b4ec-4a28-9805-08a6b80c2042"
    console.log("class id form component",classId);
       
  const navigation = useNavigation();
  const route = useRoute();
  const { styles, isDarkMode } = IndexStyle();

  const [modalVisible, setModalVisible] = useState(false);
  const [thoughtInput, setThoughtInput] = useState('');
  const [thoughtsList, setThoughtsList] = useState([]);
  const [showError, setShowError] = useState(false);
  const [loading, setLoading] = useState(false);
  const [hasMoreData, setHasMoreData] = useState(true);
  const [page, setPage] = useState(1);
  const [errorMessage, setErrorMessage] = useState<string | null>(null);
  const [editingThoughtId, setEditingThoughtId] = useState<string | null>(null);

  const limit = 10; // Number of thoughts per page

//   type Thought = {
//     id: string;
//     thoughts: string; 
//     createdAt: string;
//   };

  // Assume classId is passed via route params

  // Fetch thoughts with pagination
  const getThoughtsList = async (pageNumber = 1) => {
    if (loading || !hasMoreData || !classId) {
        console.log('Skipping fetch: loading=', loading, 'hasMoreData=', hasMoreData, 'classId=', classId);
        return;
    }
    setLoading(true);
    setErrorMessage(null);
    console.log("hello from the get thought list");
    
    try {
      const res = await api.TutorProfile.ThoughtApi.getThoughts(classId, pageNumber, limit);
      console.log(res);
      
    //   console.log(Fetch Thoughts Response Status (Page ${pageNumber}):, res.status);
      if (!res.ok) {
        console.log('API Error:', res.status, res.statusText);
        setHasMoreData(false);
        setLoading(false);
        return;
      }
      const text = await res.text();
      console.log('RAW RESPONSE TEXT::', text);
      if (!text) {
        console.log('Empty response received');
        setHasMoreData(false);
        // setLoading(false);
        return;
      }
      const jsonData = JSON.parse(text);
      const newData = jsonData.thoughts?.map((item: any) => ({
        id: item.id,
        thoughts: item.thoughts,
        createdAt: item.createdAt,
      })) || [];
    // const newData = jsonData.data;

      
      if (pageNumber === 1) {
        setThoughtsList(newData);
      } else {
        setThoughtsList(prev => [...(prev || []), ...newData]);
      }
      console.log('Updated thoughtsList:', newData); // Log after state update
      if (newData.length < limit) {
        console.log('Last data, length:', newData.length);
        setHasMoreData(false);
      } else {
        setPage(pageNumber + 1);
      }
    } catch (err) {
      console.error('ERROR IN GET THOUGHTS LIST::', err);
      setErrorMessage(strings.Thoughts.FETCHERROR);
    } finally {
      setLoading(false);
    }
  };

  // Initial fetch
  useEffect(() => {
    setPage(1);
    setHasMoreData(true);
    setThoughtsList([]);
    getThoughtsList(1);
  }, [classId]);

  useEffect(() => {
    console.log('thoughtsList updated:', thoughtsList);
  }, [thoughtsList]);

//   const handleSaveThought = async () => {
//     if (!thoughtInput.trim()) {
//       setShowError(true);
//       return;
//     }
//     if (thoughtInput.length < 50 || thoughtInput.length > 500) {
//       setShowError(true);
//       Toast.show(strings.Thoughts.INPUTVALIDATIONERROR,Toast.SHORT)
//     //   Alert.alert(strings.Thoughts.INPUTVALIDATIONERROR);
//       return;
//     }

//     try {
      
//       const response = await api.TutorProfile.ThoughtApi.saveThought({
//         classId,
//         thoughts: thoughtInput, 
//       });

//       if (response.ok) {
//         setThoughtInput('');
//         setModalVisible(false);
//         // Reset to page 1 to include new thought
//         setPage(1);
//         setHasMoreData(true);
//         setThoughtsList([]);
//         getThoughtsList(1);
//         Toast.show(strings.Thoughts.THOUGHTSAVEDSUCCESS,Toast.SHORT)
//       } else {
//         const errorText = await response.text();
//         console.error('Failed to save thought:', errorText);
//         Toast.show(strings.Thoughts.THOUGHTSAVEERROR,Toast.SHORT)

//         // Alert.alert(strings.Thoughts.THOUGHTSAVEERROR);
//       }
//     } catch (error) {
//       console.error('Error saving thought:', error);
//       Toast.show(strings.Thoughts.THOUGHTSAVEERROR,Toast.SHORT)

//     //   Alert.alert(strings.Thoughts.THOUGHTSAVEERROR);
//     }
//   };

const handleSaveThought = async () => {
    if (!thoughtInput.trim()) {
      setShowError(true);
      return;
    }
    if (thoughtInput.length < 50 || thoughtInput.length > 500) {
      setShowError(true);
      Toast.show(strings.Thoughts.INPUTVALIDATIONERROR, Toast.SHORT);
      return;
    }
  
    try {
      let response;
      if (editingThoughtId) {
        // Update existing thought
        response = await api.TutorProfile.ThoughtApi.updateThought(editingThoughtId, thoughtInput);
      } else {
        // Create new thought
        response = await api.TutorProfile.ThoughtApi.saveThought({
          classId,
          thoughts: thoughtInput,
        });
      }
  
      if (response.ok) {
        setThoughtInput('');
        setModalVisible(false);
        setEditingThoughtId(null);  // reset edit state
        // Refresh list from page 1
        setPage(1);
        setHasMoreData(true);
        setThoughtsList([]);
        getThoughtsList(1);
        Toast.show(editingThoughtId ? strings.Thoughts.THOUGHTUPDATESUCCESS : strings.Thoughts.THOUGHTSAVEDSUCCESS, Toast.SHORT);
      } else {
        const errorText = await response.text();
        console.error(editingThoughtId ? 'Failed to update thought:' : 'Failed to save thought:', errorText);
        Toast.show(editingThoughtId ? strings.Thoughts.THOUGHTUPDATEERROR : strings.Thoughts.THOUGHTSAVEERROR, Toast.SHORT);
      }
    } catch (error) {
      console.error(editingThoughtId ? 'Error updating thought:' : 'Error saving thought:', error);
      Toast.show(editingThoughtId ? strings.Thoughts.THOUGHTUPDATEERROR : strings.Thoughts.THOUGHTSAVEERROR, Toast.SHORT);
    }
  };
  
  const deleteThought = async (id: string) => {
    Alert.alert(strings.Thoughts.CONFIRMDELETE, strings.Thoughts.DELETEASKINGSTRING, [
      { text: strings.Thoughts.CANCEL, style: 'cancel' },
      {
        text: strings.Thoughts.DELETE,
        style: 'destructive', 
        onPress: async () => {
          try {
            // Placeholder for delete API
            const response = await api.TutorProfile.ThoughtApi.deleteThought(id);
            if (response.ok) {
              // Reset to page 1 to reflect deletion
              setPage(1);
              setHasMoreData(true);
              setThoughtsList([]);
              getThoughtsList(1);
              Toast.show(strings.Thoughts.THOUGHTDELETEDSUCCESS, Toast.SHORT);

            //   Alert.alert(strings.Thoughts.THOUGHTDELETEDSUCCESS);
            } else {
              console.error('Failed to delete thought');
              Alert.alert(strings.Thoughts.THOUGHTDELETEERROR);
            }
          } catch (error) {
            console.error('Error deleting thought:', error);
            Alert.alert(strings.Thoughts.THOUGHTDELETEERROR);
          }
        },
      },
    ]);
  };


const renderItem = ({ item }) => {
    const formattedDate = new Date(item.createdAt).toLocaleDateString(); // Format date
  
    return (
      <View
        style={[{
          padding: 12,
          borderWidth: 1,
          borderColor: isDarkMode ? PrimaryColors.WHITE : PrimaryColors.BLACK,
          borderRadius: 10,
          margin: 15,
        },styles.backgroundShadow]}
      >
        {/* Top Buttons */}
        <View style={{ flexDirection: 'row', justifyContent: 'flex-end' }}>
          <TouchableOpacity
            style={{
              marginLeft: 10,
              backgroundColor:'blue',
              paddingHorizontal: 10,
              paddingVertical: 5,
              borderRadius: 5,
            }}
            onPress={() => {
                setEditingThoughtId(item.id);
                setThoughtInput(item.thoughts);
                setShowError(false);
                setModalVisible(true);
              }}
          >
            <Text style={{ color: PrimaryColors.WHITE, fontWeight: 'bold' }}>
              {strings.Thoughts.EDIT }
            </Text>
          </TouchableOpacity>
  
          <TouchableOpacity
            onPress={() => deleteThought(item.id)}
            style={{
              marginLeft: 10,
              backgroundColor: PrimaryColors.RED,
              paddingHorizontal: 10,
              paddingVertical: 5,
              borderRadius: 5,
            }}
          >
            <Text style={{ color: PrimaryColors.WHITE, fontWeight: 'bold' }}>
              {strings.Thoughts.DELETE}
            </Text>
          </TouchableOpacity>
          
        </View>
  
        {/* Thought Label */}
        <Text
          style={{
            marginTop: 10,
            fontWeight: '600',
            color: isDarkMode ? PrimaryColors.WHITE : PrimaryColors.BLACK,
            fontSize:20,
            marginBottom:'1%'
          }}
        >
          {strings.Thoughts.THOUGHT1}
        </Text>
  
        {/* Truncated Thought Text */}
        <Text
          numberOfLines={3}
          ellipsizeMode="tail"
          style={{ color: isDarkMode ? PrimaryColors.WHITE : PrimaryColors.BLACK }}
        >
          {item.thoughts}
        </Text>
  
        {/* Created At Label */}
        <Text
          style={{
            marginTop: 8,
            fontStyle: 'italic',
            fontSize: 12,
            color: isDarkMode ? '#aaa' : '#555',
          }}
        >
          {strings.Thoughts.CREATEDAT} {formattedDate}
        </Text>
      </View>
    );
  };
  
  console.log(Array.isArray(thoughtsList), thoughtsList.length);


  return (
    <SafeAreaProvider style={styles.container}>
      <Header title={strings.Thoughts.THOUGHTS} />
      <SafeAreaView style={{ width: '100%', paddingHorizontal: '5%' }}>
        <View style={{ flex: 1, marginTop: '2%' }}>
          <View style={{ alignItems: 'center', marginBottom: 20, }}>
            <TouchableOpacity
              style={{
                borderRadius: 10,
                height: 50,
                justifyContent: 'center',
                alignItems: 'center',
                width: '40%',
                borderWidth: 1,
                borderColor: isDarkMode ? PrimaryColors.WHITE : PrimaryColors.BLACK,
              }}
              onPress={() => {
                setModalVisible(true);
                setShowError(false);
                setThoughtInput('');
              }}
            >
              <Text
                style={{
                  color: isDarkMode ? PrimaryColors.WHITE : PrimaryColors.BLACK,
                  fontWeight: '600',
                  fontSize: 16,
                }}
              >
                ➕ {strings.Thoughts.ADDTHOUGHT}
              </Text>
            </TouchableOpacity>
          </View>
          {errorMessage && (
            <Text style={{ textAlign: 'center', color: PrimaryColors.RED, marginBottom: 10 }}>
              {errorMessage}
            </Text>
          )}
          {/* {console.log(thoughtsList)
          } */}
          <View style={{position:'absolute',top:50,width:"100%"
          }}>
          <FlatList
            
            data={thoughtsList}
            keyExtractor={(item) => item.id.toString()}
            renderItem={renderItem}
            contentContainerStyle={{ padding: 16 }}
            onEndReached={() => {
              if (!loading && hasMoreData) {
                getThoughtsList(page);
              }
            }}
            onEndReachedThreshold={0.5}
            // ListEmptyComponent={
            //   !loading && (
            //     <Text style={{ textAlign: 'center', color: isDarkMode ? '#aaa' : '#555' }}>
            //       {strings.Thoughts.NODATAFOUND}
            //     </Text>
            //   )
            // }
             ListFooterComponent={
              loading ? (
                <View style={{ paddingVertical: 16, alignItems: 'center' }}>
                  <ActivityIndicator
                    size="small"
                    color={isDarkMode ? PrimaryColors.WHITE : PrimaryColors.BLACK}
                  />
                </View>
              ) : null
            }
          /> 
        </View>
        </View>
      </SafeAreaView>
      <Modal
        visible={modalVisible}
        transparent
        animationType="fade"
        onRequestClose={() => {
          setModalVisible(false);
          setShowError(false);
          setThoughtInput('');
        }}
      >
        <View style={{ justifyContent: 'center', alignItems: 'center', flex: 1, margin: 15 }}>
          <View style={styles.modalView}>
            <Text style={[styles.label, { marginBottom: 10, fontSize: 20 }]}>
              {strings.Thoughts.CREATETHOUGHT}
            </Text>
            <Text style={[styles.label, { marginBottom: 10 }]}>{strings.Thoughts.THOUGHT}</Text>
            <TextInput
              placeholder={strings.Thoughts.ENTERTHOUGHT50500CHARACTERS}
              placeholderTextColor={isDarkMode ? PrimaryColors.WHITE : PrimaryColors.BLACK}
              value={thoughtInput}
              onChangeText={text => {
                setThoughtInput(text);
                if (text.trim()) setShowError(false);
              }}
              style={[styles.input, { marginBottom: 10 }]}
              multiline
              maxLength={500}
            />
            {showError && (
              <Text style={{ color: PrimaryColors.RED, fontSize: 12, marginBottom: '5%' }}>

              </Text>
            )}
            <View style={{ flexDirection: 'row', justifyContent: 'flex-end', marginTop: 10 }}>
              <TouchableOpacity
                style={{
                  borderRadius: 10,
                  height: 45,
                  width: '22%',
                  borderWidth: 1,
                  justifyContent: 'center',
                  alignItems: 'center',
                  backgroundColor: isDarkMode ? PrimaryColors.BLACK : PrimaryColors.WHITE,
                  marginRight: 10,
                }}
                onPress={() => {
                  setModalVisible(false);
                  setShowError(false);
                  setThoughtInput('');
                }}
              >
                <Text style={{ color: isDarkMode ? PrimaryColors.WHITE : PrimaryColors.BLACK, fontWeight: '600' }}>
                  {strings.Thoughts.CANCEL}
                </Text>
              </TouchableOpacity>
                <TouchableOpacity
                style={{
                  borderRadius: 10,
                  height: 45,
                  width: '22%',
                  borderWidth: 1,
                  justifyContent: 'center',
                  alignItems: 'center',
                  backgroundColor: isDarkMode ? PrimaryColors.WHITE : PrimaryColors.BLACK,
                }}
                onPress={handleSaveThought}
              > 
                <Text style={{ color: isDarkMode ? PrimaryColors.BLACK : PrimaryColors.WHITE, fontWeight: '600' }}>
                  {strings.Thoughts.SAVE}
                </Text>
              </TouchableOpacity> 
              
            </View>
          </View>
        </View>
      </Modal>
    </SafeAreaProvider>
  );
};

export default Thoughts;