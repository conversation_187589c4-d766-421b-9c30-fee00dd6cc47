// App/Screens/Login/Login.tsx

import React, { useState } from 'react';
import { View, Text, TouchableOpacity } from 'react-native';
import { TextInput } from 'react-native-gesture-handler';
import { PrimaryColors } from '../../../Utils/Constants';
import { useNavigation } from '@react-navigation/native';
import strings from '../../../Utils/LocalizedStrings/LocalizedStrings';
import api from '../../../config/api';
import IndexStyle from '../../../Theme/IndexStyle';
import HeaderWithLogo from '../../../CommonComponents/HeaderWithLogo';
import Button from '../../../CommonComponents/Button';
import Toast from 'react-native-simple-toast';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { useDispatch } from 'react-redux';
// import {  } from '@reduxjs/toolkit';
import { setUserData } from '../../../Redux/userSlice';
import CommonTextInput from '../../../CommonComponents/CommonTextInput';

const TutorLogin = () => {
  const dispatch = useDispatch()
  const navigation = useNavigation<any>();
  const { styles, isDarkMode } = IndexStyle();
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [isemail, setIsemail] = useState(false);
  const [ispassword, setIspassword] = useState(false);
  const [invalidCred, setinvalidcred] = useState(false);

  const Login = () => {
    console.log("EMAIL:::", email, " PASS::", password);

    const isEmailEmpty = email.trim() === '';
    const isPasswordEmpty = password.trim() === '';

    setIsemail(false);
    setIspassword(false);

    if (isEmailEmpty || isPasswordEmpty) {
      if (isEmailEmpty) setIsemail(true);
      if (isPasswordEmpty) setIspassword(true);
      return;
    }

    let loginData = {
      email: email,
      password: password
    };

    api.TutorLogin.tutorLogin(loginData)
      .then(async response => {
        if (response.ok) {
          const resData = await response.json();
          console.log("Login Response:", resData);
          console.log("UserData with login", resData.data.user);
          dispatch(setUserData(resData.data.user));

          const tutorToken = await AsyncStorage.setItem('token', resData.data.token);

          if (tutorToken === null) {
            Toast.show(strings.Login.LOGINFAILED, Toast.SHORT);
          }
          else {
            Toast.show(strings.Login.LOGINSUCCESSFUL, Toast.SHORT);
            navigation.navigate('TutorHome');
          }
        } else if (response.status === 400) {
          Toast.show(strings.Login.SOMETHINGWENTWRONG, Toast.SHORT);
        } else if (response.status === 403) {
          Toast.show(strings.Login.INVALIDCREDENTIALS, Toast.SHORT);
        } else if (response.status === 404) {
          Toast.show(strings.Login.USERNOTFOUND, Toast.SHORT);
        } else {
          console.log("Login failed with status", response.status);
          setinvalidcred(true);
        }
      })
      .catch(error => {
        console.error("Login Error:", error);
        setinvalidcred(true);
      });
  };

  return (
    <View style={styles.container}>
      <HeaderWithLogo />
      <View style={{ width: '80%', marginBottom: '2%' }}>
        <CommonTextInput
          label={strings.Login.EMAIL}
          placeholder={strings.Login.EMAIL}
          value={email}
          onChangeText={setEmail}
          style={{ width: '100%' }}
        />
        {isemail ? (
          <Text style={styles.error}>{strings.Login.PLEASENETEREMAIL}</Text>
        ) : (
          <></>
        )}
      </View>

      <View style={{ width: '80%', marginBottom: '2%' }}>
        <CommonTextInput
          label={strings.Login.PASSWORD}
          placeholder={strings.Login.PASSWORD}
          value={password}
          onChangeText={setPassword}
          style={{ width: '100%' }}
        />
        {ispassword && (
          <Text style={styles.error}>{strings.Login.PLEASENTERPASSWORD}</Text>
        )}
      </View>
      
      <Button title={strings.Login.LOGIN} onPress={Login} />

      {invalidCred && (
        <Text style={styles.error}>{strings.Login.INVALIDCRED}</Text>
      )}

      <TouchableOpacity
        style={[{ marginTop: 16, width: '80%', alignItems: 'center' }]}
        onPress={() => navigation.navigate('ForgotPassword')}>
        <Text style={{
          color: isDarkMode ? PrimaryColors.WHITE : PrimaryColors.BLACK,
          fontSize: 18,
          fontWeight: '600',
        }}>
          {strings.Login.FORGOTPASSWORD}
        </Text>
      </TouchableOpacity>

      <TouchableOpacity
        style={[{ marginTop: 16, width: '80%', alignItems: 'center' }]}
        onPress={() => navigation.navigate('RegisterTutor')}>
        <Text style={{
          color: isDarkMode ? PrimaryColors.WHITE : PrimaryColors.BLACK,
          fontSize: 18,
          fontWeight: '600',
        }}>
          {strings.Login.REGISTERUSER}
        </Text>
      </TouchableOpacity>
    </View>
  );
};

export default TutorLogin;
