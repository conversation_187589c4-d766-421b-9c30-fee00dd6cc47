// App/Screens/Login/Login.tsx
import React, { useEffect, useState } from 'react';
import { View, Text, StyleSheet, Switch, TouchableOpacity, Alert } from 'react-native';
import { SafeAreaView, SafeAreaProvider } from 'react-native-safe-area-context';
import { IMAGE_CONSTANT, PrimaryColors } from '../../../Utils/Constants';
import { useNavigation } from '@react-navigation/native';
import strings from '../../../Utils/LocalizedStrings/LocalizedStrings';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { useDispatch } from 'react-redux';
import { setDarkMode } from '../../../Redux/themeSlice';
import Toggle from '../../../CommonComponents/Toggle';
import IndexStyle from '../../../Theme/IndexStyle';


const TutorSetting = () => {
  const navigation = useNavigation();
  const { styles } = IndexStyle()
  const dispatch = useDispatch();
  const [isDarkMode, setIsDarkMode] = useState(false);
  const BaseStyle = isDarkMode ? DarkStyles : LightStyles;
  useEffect(() => {
    const getStoredTheme = async () => {
      try {
        const storedMode = await AsyncStorage.getItem('isDarkMode');
        const parsedMode = storedMode !== null ? JSON.parse(storedMode) : false;
        setIsDarkMode(parsedMode);
        dispatch(setDarkMode(parsedMode));
      } catch (error) {
        console.error('Failed to load theme mode:', error);
      }
    };

    getStoredTheme();
  }, []);
  const toggleSwitch = async () => {
    const newMode = !isDarkMode;
    setIsDarkMode(newMode);
    dispatch(setDarkMode(newMode));
    await AsyncStorage.setItem('isDarkMode', JSON.stringify(newMode));

  }

  const logout = () => {
    Alert.alert(
      'Logout',
      'Are you sure you want to logout?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Logout',
          style: 'destructive',
          onPress: async () => {
            try {
              await AsyncStorage.removeItem('token');
              navigation.navigate('UseAppAs');
            } catch (error) {
              console.error('Logout error:', error);
            }
          }
        }
      ]
    );
  };

  return (
    <>
      {console.log("MODE VALUE AT RENDER::: ISDATKMDOE?:::", isDarkMode)

      }
      <SafeAreaProvider>
        <SafeAreaView style={styles.settingContainer}>
          <Text style={BaseStyle.title}>{strings.Setting.SETTING}</Text>

          <View style={BaseStyle.card}>
            <View style={BaseStyle.row}>
              <Text style={BaseStyle.optionLabel}>{strings.Setting.MODE}</Text>

              <Toggle value={isDarkMode} onToggle={toggleSwitch} />

            </View>

            <View style={BaseStyle.divider} />

            <TouchableOpacity onPress={logout}>
              <Text style={[styles.mainTextColor, styles.settingItemText]}>{strings.Setting.LOGOUT}</Text>
            </TouchableOpacity>
          </View>
        </SafeAreaView>
      </SafeAreaProvider>
    </>
  );
};

export default TutorSetting;

const DarkStyles = StyleSheet.create({

  title: {
    fontSize: 24,
    fontWeight: '700',
    color: PrimaryColors.WHITE,
    marginBottom: 20,
  },
  card: {
    backgroundColor: 'transparent',
    borderColor: PrimaryColors.WHITE,
    borderWidth: 2,
    borderRadius: 10,
    padding: 20,
    marginLeft: 5,
    marginRight: 5,
  },
  row: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  optionLabel: {
    fontSize: 18,
    color: PrimaryColors.WHITE,
  },

  divider: {
    height: 1,
    backgroundColor: PrimaryColors.WHITE,
    marginVertical: 20,
    marginLeft: 5,
    marginRight: 5,
  },

});

const LightStyles = StyleSheet.create({

  title: {
    fontSize: 24,
    fontWeight: '700',
    color: PrimaryColors.BLACK,
    marginBottom: 20,
  },
  card: {
    backgroundColor: 'transparent',
    borderColor: PrimaryColors.BLACK,
    borderWidth: 2,
    borderRadius: 10,
    padding: 20,
    marginLeft: 5,
    marginRight: 5,
  },
  row: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  optionLabel: {
    fontSize: 18,
    color: PrimaryColors.BLACK,
  },
  switch: {
    transform: [{ scaleX: 1.3 }, { scaleY: 1.3 }],
    borderWidth: 2,
    borderColor: PrimaryColors.BLACK
  },
  divider: {
    height: 1,
    backgroundColor: PrimaryColors.BLACK,
    marginVertical: 20,
    marginLeft: 5,
    marginRight: 5,
  }

});
