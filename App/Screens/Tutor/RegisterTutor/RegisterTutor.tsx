import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TextInput,
  TouchableOpacity,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { PrimaryColors } from '../../../Utils/Constants';
import strings from '../../../Utils/LocalizedStrings/LocalizedStrings';
import HeaderWithLogo from '../../../CommonComponents/HeaderWithLogo';
import IndexStyle from '../../../Theme/IndexStyle';
import Button from '../../../CommonComponents/Button';
import api from '../../../config/api';
import Toast from 'react-native-simple-toast';
import CommonTextInput from '../../../CommonComponents/CommonTextInput';


const Registration = () => {
  const navigation = useNavigation<any>();
  const { styles, isDarkMode } = IndexStyle();
  const [firstName, setFirstName] = useState('');
  const [lastName, setLastName] = useState('');
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');

  const [errors, setErrors] = useState({
    firstName: false,
    lastName: false,
    email: false,
    password: false,
  });

  const [apiError, setApiError] = useState<string | null>(null);

  const handleRegister = async () => {
    console.log("button clicked");

    const trimmed = {
      firstName: firstName.trim(),
      lastName: lastName.trim(),
      email: email.trim(),
      password: password.trim(),
    };

    const newErrors = {
      firstName: trimmed.firstName === '',
      lastName: trimmed.lastName === '',
      email: trimmed.email === '',
      password: false,
    };

    setApiError(null);

    if (trimmed.password === '') {
      newErrors.password = true;
      setApiError(strings.Registration.PLEASEENTEREMAIL);
    } else if (trimmed.password.length < 6) {
      newErrors.password = true;
      setApiError(strings.Registration.PASSWORDLENGTH);
    }

    setErrors(newErrors);


    if (Object.values(newErrors).some(Boolean)) {
      return;
    }

    try {
      const registrationData = trimmed;

      api.TutorRegistration.tutorRegister(registrationData)
        .then((response) => {
          console.log("Registration API response", response);

          if (response.status === 200) {
            response.json().then((jsondata) => {
              console.log("API response data", jsondata);
              Toast.show(strings.Registration.REGISTRATIONSUCCESSFUL, Toast.SHORT);
              navigation.navigate('Login');
            }).catch((err) => {
              console.log("Error parsing response data", err);
            });
          }
          else if (response.status === 400) {
            Toast.show(strings.Registration.SOMETHINGWENTWRONG, Toast.SHORT);
          }
          else if (response.status === 403) {
            Toast.show(strings.Registration.INVALIDCREDENTIALS, Toast.SHORT);
          }
          else if (response.status === 404) {
            Toast.show(strings.Registration.USERNOTFOUND, Toast.SHORT);
          }
          else {
            response.json().then((errData) => {
              console.log("API error response", errData);
              setApiError(errData.message || strings.Registration.REGISTRATIONFAILED);
            });
          }
        })
        .catch((error) => {
          console.log("Error in registration:", error.message || error);
          setApiError(strings.Registration.NETWORKERRORORSERVERUNREACHABLE);
        });

    } catch (error) {
      console.log("Unexpected error:", error);
      setApiError(strings.Registration.UNEXPECTEDERROROCCURED);
    }
  };

  return (
    <View style={styles.container}>

      <HeaderWithLogo />

      <View style={[BaseStyle.inputContainer, { marginBottom: '2%' }]}>
        <CommonTextInput
          label={strings.Registration.FIRSTNAME}
          placeholder={strings.Registration.ENTERFIRSTNAME}
          value={firstName}
          onChangeText={setFirstName}
          style={{ width: '100%' }}
        />
        {errors.firstName && (
          <Text style={styles.error}>
            {strings.Registration.PLEASEENTERFIRSTNAME}
          </Text>
        )}
      </View>

      <View style={[BaseStyle.inputContainer, { marginBottom: '2%' }]}>
        <CommonTextInput
          label={strings.Registration.LASTNAME}
          placeholder={strings.Registration.ENTERLASTNAME}
          value={lastName}
          onChangeText={setLastName}
          style={{ width: '100%' }}
        />
        {errors.lastName && (
          <Text style={styles.error}>
            {strings.Registration.PLEASEENTERLASTNAME}
          </Text>
        )}
      </View>

      <View style={[BaseStyle.inputContainer, { marginBottom: '2%' }]}>
        <CommonTextInput
          label={strings.Registration.EMAIL}
          placeholder={strings.Registration.ENTEREMAIL}
          value={email}
          onChangeText={setEmail}
          keyboardType="email-address"
          style={{ width: '100%' }}
        />
        {errors.email && (
          <Text style={styles.error}>
            {strings.Registration.PLEASEENTEREMAIL}
          </Text>
        )}
      </View>

      <View style={[BaseStyle.inputContainer, { marginBottom: '2%' }]}>
        <CommonTextInput
          label={strings.Registration.PASSWORD}
          placeholder={strings.Registration.ENTERPASSWORD}
          value={password}
          onChangeText={setPassword}
          style={{ width: '100%' }}
        />
        {errors.password && <Text style={styles.error}>{apiError}</Text>}
      </View>

      <Button title={strings.Registration.REGISTER} onPress={handleRegister} />
      <TouchableOpacity onPress={() => navigation.navigate('TutorLogin')} style={{ marginTop: 16 }}>
        <Text style={{ color: PrimaryColors.ORANGE, fontSize: 16, fontWeight: '500', }}>{strings.Registration.ALREADYHAVEACCOUNT}</Text>
      </TouchableOpacity>
    </View>
  );
}


export default Registration;

const BaseStyle = StyleSheet.create({
  inputContainer: {
    width: '80%',
    marginBottom: 20,
  },
})
