import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, ScrollView, FlatList } from 'react-native';
import { IMAGE_CONSTANT, PrimaryColors } from '../../../../Utils/Constants';
import { useNavigation } from '@react-navigation/native';
import LinearGradient from 'react-native-linear-gradient';
import { SafeAreaView, SafeAreaProvider } from 'react-native-safe-area-context';
import Ionicons from 'react-native-vector-icons/Ionicons';
import Header from '../../../../CommonComponents/Header';
import strings from '../../../../Utils/LocalizedStrings/LocalizedStrings';
import CommonTextInput from '../../../../CommonComponents/CommonTextInput';
import IndexStyle from '../../../../Theme/IndexStyle';
import CheckBox from 'react-native-check-box';
import Button from '../../../../CommonComponents/Button';
import CommonCheckbox from '../../../../CommonComponents/CommonCheckbox';
import CommonDateTimePicker from '../../../../CommonComponents/CommonDateTimePicker';
import AsyncStorage from '@react-native-async-storage/async-storage';
import api from '../../../../config/api';
import { RootState } from '../../../../Redux/store';
import { useSelector } from 'react-redux';
import Toast from 'react-native-simple-toast';


const About = () => {
  const user = useSelector((state: RootState) => state.user);
  // console.log("USER FROM ABOUT",user);
  
  const navigation = useNavigation<any>();
  const { styles, isDarkMode } = IndexStyle();

  const [username, setUsername] = useState('');
  const [firstName, setFirstName] = useState('');
  const [lastName, setLastName] = useState('');
  const [className, setClassName] = useState('');
  const [email, setEmail] = useState('');
  const [birthDate, setBirthDate] = useState<Date | null>(null);
  const [contactNo, setContactNo] = useState('');
  const [isChecked, setIsChecked] = useState(false);
  const [showEditor, setShowEditor] = useState(false);

  useEffect(() => {
    setTimeout(() => setShowEditor(true), 100);
    fetchAboutData();
  }, []);

  const handleUpdateProfile = async () => {
    try {
      const payload = {
        classId: user.userData?.id,
        username,
        firstName,
        lastName,
        className,
        email,
        birthDate: birthDate ? birthDate.toISOString() : null,
        contactNo,
        isAdult: isChecked,
      };
  
      console.log("Sending update payload:", payload);
  
      const response = await api.TutorProfile.TutorAbout.updateAbout(payload);
  
      if (response.ok) {
        const resultText = await response.text();
        console.log("Update success:", resultText);
        Toast.show("Profile Updated Successfully", Toast.SHORT);
        navigation.navigate('Description')
      }
      else if(response.status===400)
      {
        Toast.show("Email And UserName should be unique",Toast.SHORT)
      }
       else {
        console.log("Update failed. Status:", response);
      }
    } catch (error) {
      console.log("Error while updating profile:", error);
    }
  };
  

const userId=user.userData?.id
console.log(userId);

  const fetchAboutData = async () => {
    try {
      
      const response = await api.GetProfileData.getProfileData(userId);

      if (response.ok) {
        const text = await response.text();
        console.log("RAW RESPONSE TEXT::", text);

        const jsonData = JSON.parse(text);
        console.log("PARSED JSON DATA::", jsonData);

        console.log("PARSED DATE::", jsonData.ClassAbout.birthDate);


        setUsername(jsonData.username || '');
        setFirstName(jsonData.firstName || '');
        setLastName(jsonData.lastName || '');
        setClassName(jsonData.className || '');
        setEmail(jsonData.email || '');
        setContactNo(jsonData.contactNo || '');
        setIsChecked(jsonData.isAdult || false);

        if (jsonData.ClassAbout.birthDate) {
          const parsedDate = new Date(jsonData.ClassAbout.birthDate);
          if (!isNaN(parsedDate.getTime())) {
            setBirthDate(parsedDate);
          }
        }
      } else {
        console.log("Failed to About data. Status:", response.status);
      }
    } catch (err) {
      console.log("ERROR IN GET ABOUT DATA::", err);
    }
  };

  const formatDate = (date: Date | null) => {
    if (!date) return '';
    const day = date.getDate().toString().padStart(2, '0');
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const year = date.getFullYear();
    return `${day}/${month}/${year}`;
  };


  return (
    <SafeAreaProvider style={styles.container}>
      <Header title={strings.About.ABOUT} />
      <SafeAreaView style={{ flex: 1, width: '100%', justifyContent: 'center', alignItems: 'center' }}>
        <CommonTextInput label={strings.About.USERNAME} placeholder={strings.About.ENTERUSERNAME} value={username} onChangeText={setUsername} />
        <CommonTextInput label={strings.About.FIRSTNAME} placeholder={strings.About.ENTERFIRSTNAME} value={firstName} onChangeText={setFirstName} />
        <CommonTextInput label={strings.About.LASTNAME} placeholder={strings.About.ENTERLASTNAME} value={lastName} onChangeText={setLastName} />
        <CommonTextInput label={strings.About.CLASSNAME} placeholder={strings.About.ENTERCLASSNAME} value={className} onChangeText={setClassName} />
        <CommonTextInput label={strings.About.EMAIL} placeholder={strings.About.ENTEREMAIL} value={email} onChangeText={setEmail} />
        <CommonDateTimePicker label={strings.About.DATEOFBIRTH} value={birthDate} innerText={formatDate(birthDate) || 'Select BirthDate'} onChange={setBirthDate} mode='date' />
        <CommonTextInput label={strings.About.CONTACTNUMBER} placeholder={strings.About.ENTERCONTACTNUMBER} value={contactNo} onChangeText={setContactNo} keyboardType="numeric" />
        <CommonCheckbox isChecked={isChecked} onClick={() => setIsChecked(!isChecked)} label={strings.About.ICONFIRMIAMOVEREIGHTEEN} />

        <Button title={strings.About.UPDATEPROFILE} onPress={handleUpdateProfile} />
      </SafeAreaView>
    </SafeAreaProvider>
  );
};

export default About;
