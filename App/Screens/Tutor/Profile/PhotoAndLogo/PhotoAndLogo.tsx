import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, Image, Alert, TouchableOpacity } from 'react-native';
import { PrimaryColors } from '../../../../Utils/Constants';
import { useNavigation } from '@react-navigation/native';
import { SafeAreaView, SafeAreaProvider } from 'react-native-safe-area-context';
import { launchImageLibrary } from 'react-native-image-picker';
import Header from '../../../../CommonComponents/Header';
import strings from '../../../../Utils/LocalizedStrings/LocalizedStrings';
import Button from '../../../../CommonComponents/Button';
import IndexStyle from '../../../../Theme/IndexStyle';
import { PermissionsAndroid, Platform } from 'react-native';
import { request, PERMISSIONS, RESULTS } from 'react-native-permissions';
import api from '../../../../config/api';
import Toast from 'react-native-simple-toast';
import { apiUrl } from '../../../../config/apiUrl';



const PhotoAndLogo = () => {
    const navigation = useNavigation();
    const { styles, isDarkMode } = IndexStyle();
    const [profileImage, setProfileImage] = useState<string | null>(null);
    const [classLogo, setClassLogo] = useState<string | null>(null);
    const [savedImages, setSavedImages] = useState<{ profileImage: string | null; classLogo: string | null }>({
        profileImage: null,
        classLogo: null,
    });


    const requestStoragePermission = async () => {
        if (Platform.OS === 'android') {
            const granted = await PermissionsAndroid.request(
                PermissionsAndroid.PERMISSIONS.READ_MEDIA_IMAGES,
                {
                    title: strings.PhotoAndLogo.STORAGEPERMISSIONTITLE,
                    message: strings.PhotoAndLogo.APPNEEDSACCESSTOYOURPHOTOGALLERY,
                    buttonNeutral: strings.PhotoAndLogo.ASKMELATER,
                    buttonNegative: strings.PhotoAndLogo.CANCEL,
                    buttonPositive: strings.PhotoAndLogo.OK,
                }
            );
            return granted === PermissionsAndroid.RESULTS.GRANTED;
        } 
        // else if (Platform.OS === 'ios') {
        //     const result = await request(PERMISSIONS.IOS.PHOTO_LIBRARY);
        //     return result === RESULTS.GRANTED || result === RESULTS.LIMITED;
        // }
        return true;
    };

    const pickImage = async (type: 'profile' | 'class') => {
        const hasPermission = await requestStoragePermission();
        if (!hasPermission) {
            // Alert.alert('Permission Denied', 'Cannot access gallery.');
            Alert.alert(strings.PhotoAndLogo.PERMISSIONDENIED, strings.PhotoAndLogo.CANNOTACCESSGALLERY);
            return;
        }

        
        launchImageLibrary({ mediaType: 'photo' }, (response) => {
            if (response.didCancel || response.errorCode) {
                // Alert.alert('Cancelled', 'Image selection was cancelled.');
                Alert.alert(strings.PhotoAndLogo.CANCELLED, strings.PhotoAndLogo.CANCELLEDMESSAGE);
                return;
            }
            console.log('Image picker response:', response);
      
            const selected = response.assets?.[0];
            if (selected?.uri) {
              // Check file size (in bytes, 2MB = 2,000,000 bytes)
              const maxSize = 2 * 1000 * 1000; // 2MB in bytes
              if (selected.fileSize && selected.fileSize >= maxSize) {
                Alert.alert('File Too Large', 'The selected image must be less than 2MB.');
                return;
              }
              console.log(`Selected ${type} image URI:`, selected.uri);
              if (type === 'profile') {
                setProfileImage(selected.uri);
              } else {
                setClassLogo(selected.uri);
              }
            }
          });
    };
  

    console.log(profileImage, classLogo);


    useEffect(() => {
        // `${apiUrl}/classes/details/${classId}` ---> SET CLASS ID IN ASYNC STORAGE
        fetch(`${apiUrl}/classes/details/efd64e13-b4ec-4a28-9805-08a6b80c2042`)
            .then(res => res.json())
            .then(data => {
                const base = 'https://www.uest.in/uapi/';
                setProfileImage(`${base}${data.ClassAbout.profilePhoto}`);
                setClassLogo(`${base}${data.ClassAbout.classesLogo}`);
            })
            .catch(err => {
                console.log('Failed to load images', err);
            });
    }, []);

    const handleSave = async() => {
        if (!profileImage && !classLogo) {
            Alert.alert(strings.PhotoAndLogo.NOIMAGES, strings.PhotoAndLogo.SELECTIMAGE);
            return;
        }
        setSavedImages({ profileImage, classLogo });
        Alert.alert('Saved', 'Images have been saved locally.');
        try {
            console.log("hello from logo and profile");
            
            const response=await api.TutorProfile.TutorPhotoAndLogo.updatePhotoAndLogo(profileImage,classLogo);
            console.log(profileImage);
            console.log(classLogo);
            
            
            const result=await response.json()
            if(response.ok)
            {
                Toast.show("Updated Successfully", Toast.SHORT);
                setSavedImages({ profileImage, classLogo });

            }
            else{
                console.log(result);
                console.log("PHOTO LOGO RESULT MESSAGE",result.message);
                
                Toast.show(result.message,Toast.SHORT)
                
            }
        } catch (error) {
            console.log(error)
            Alert.alert('upload falied,somthing went wrong');
        }
    };

    return (
        <SafeAreaProvider style={styles.container}>
            <Header title={strings.PhotoAndLogo.PHOTOANDLOGO} />
            <SafeAreaView style={{ flex: 1, width: '100%', paddingHorizontal: '20%' }}>
                <View style={{ alignContent: 'center', alignItems: 'center', marginBottom: 20 }}>
                    <Text style={[styles.label, { fontSize: 18, color: isDarkMode ? PrimaryColors.WHITE : PrimaryColors.BLACK }]}>{strings.PhotoAndLogo.DESCRIPTION1}</Text>
                    <Text style={[styles.label, { fontSize: 15, color: isDarkMode ? PrimaryColors.WHITE : PrimaryColors.BLACK }]}>{strings.PhotoAndLogo.DESCRIPTION2}</Text>
                </View>

                <View style={{ marginBottom: 15 }}>
                    <Text style={[styles.label, { fontSize: 16 }]}>{strings.PhotoAndLogo.PROFILEPHOTO}</Text>
                    <View style={customStyles.imageContainer}>
                        {profileImage ? (
                            <Image source={{ uri: profileImage }} style={customStyles.image} />
                        ) : (
                            <View style={customStyles.placeholder} />
                        )}
                        <TouchableOpacity
                            style={{
                                borderRadius: 10,
                                paddingVertical: 12,
                                paddingHorizontal: 24,
                                borderWidth: 1,
                                borderColor: isDarkMode ? PrimaryColors.WHITE : PrimaryColors.BLACK,
                            }}
                            onPress={() => pickImage('profile')}
                        >
                            <Text style={{ color: isDarkMode ? PrimaryColors.WHITE : PrimaryColors.BLACK, fontWeight: '600', fontSize: 16 }}>
                                {strings.PhotoAndLogo.UPLOADPROFILEPHOTO}
                            </Text>
                        </TouchableOpacity>
                    </View>
                </View>

                <View style={{ marginBottom: 15 }}>
                    <Text style={[styles.label, { fontSize: 16 }]}>{strings.PhotoAndLogo.CLASSESLOGO}</Text>
                    <View style={customStyles.imageContainer}>
                        {classLogo ? (
                            <Image source={{ uri: classLogo }} style={customStyles.image} />
                        ) : (
                            <View style={customStyles.placeholder} />
                        )}
                        <TouchableOpacity
                            style={{
                                borderRadius: 10,
                                paddingVertical: 12,
                                paddingHorizontal: 24,
                                borderWidth: 1,
                                borderColor: isDarkMode ? PrimaryColors.WHITE : PrimaryColors.BLACK,
                            }}
                            onPress={() => pickImage('class')}
                        >
                            <Text style={{ color: isDarkMode ? PrimaryColors.WHITE : PrimaryColors.BLACK, fontWeight: '600', fontSize: 16 }}>
                                {strings.PhotoAndLogo.UPLOADCLASSESLOGO}
                            </Text>
                        </TouchableOpacity>
                    </View>
                </View>

                <View style={{ alignItems: 'center', marginTop: 20 }}>
                    <Button
                        title={strings.PhotoAndLogo.UPLOADIMAGE}
                        onPress={handleSave}
                        disabled={!(profileImage || classLogo)}
                    />
                </View>

                {/* {savedImages.profileImage || savedImages.classLogo ? (
                    <View style={{ alignItems: 'center', marginTop: 20 }}>
                        <Text style={[styles.label, { fontSize: 16, fontWeight: '600' }]}>{strings.PhotoAndLogo.SAVEDIMAGES}</Text>
                        {savedImages.profileImage && (
                            <Image source={{ uri: savedImages.profileImage }} style={[customStyles.image, { marginTop: 10 }]} />
                        )}
                        {savedImages.classLogo && (
                            <Image source={{ uri: savedImages.classLogo }} style={[customStyles.image, { marginTop: 10 }]} />
                        )}
                    </View>
                ) : null} */}
            </SafeAreaView>
        </SafeAreaProvider>
    );
};

export default PhotoAndLogo;

const customStyles = StyleSheet.create({
    imageContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        marginVertical: 10,
    },
    image: {
        width: 130,
        height: 130,
        borderRadius: 65,
        borderColor: PrimaryColors.BLACK,
        borderWidth: 1,
        marginRight: 12,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.2,
        shadowRadius: 4,
    },
    placeholder: {
        width: 130,
        height: 130,
        borderRadius: 65,
        borderColor: PrimaryColors.BLACK,
        borderWidth: 1,
        backgroundColor: PrimaryColors.LIGHTGRAY2,
        marginRight: 12,
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.2,
        shadowRadius: 4,
    },
});