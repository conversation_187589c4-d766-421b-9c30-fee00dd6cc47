import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
} from 'react-native';
import { IMAGE_CONSTANT, PrimaryColors } from '../../../../Utils/Constants';
import { useNavigation } from '@react-navigation/native';
import LinearGradient from 'react-native-linear-gradient';
import { SafeAreaView, SafeAreaProvider } from 'react-native-safe-area-context';
import Ionicons from 'react-native-vector-icons/Ionicons';
import Header from '../../../../CommonComponents/Header';
import strings from '../../../../Utils/LocalizedStrings/LocalizedStrings';
import IndexStyle from '../../../../Theme/IndexStyle';
import CommonCheckbox from '../../../../CommonComponents/CommonCheckbox';
import EducationForm from './EducationForm';
import Button from '../../../../CommonComponents/Button';
import api from '../../../../config/api';

const Education = () => {
  const navigation = useNavigation();
  const { styles, isDarkMode } = IndexStyle();

  const [showEducationButton, setShowEducationButton] = useState(false);
  const [educationForms, setEducationForms] = useState<number[]>([]);
  const [education, setEducation] = useState<any[]>([]);

  const addEducationForm = () => {
    setEducationForms(prev => [...prev, Date.now()]);
  };

  useEffect(() => {
    fetchEducationData();
  }, []);

  const fetchEducationData = async () => {
    try {
      const response = await api.GetProfileData.getProfileData(
        'efd64e13-b4ec-4a28-9805-08a6b80c2042'
      );

      if (response.ok) {
        const text = await response.text();
        const jsonData = JSON.parse(text);
        const educationData = jsonData.education || [];
        setEducation(educationData);
        console.log("PARSED  DATA::", jsonData);
        console.log("Education  DATA::", educationData);
      } else {
        JSON
        console.log('Failed to fetch data. Status:', response.status);
      }
    } catch (err) {
      console.log('ERROR IN GET EDUCATION DATA::', err);
    }
  };

  const handleSaveEduction=async()=>{
    const hasnoDegree = showEducationButton;
  }

  return (
    <SafeAreaProvider style={styles.container}>
      <Header title={strings.Education.EDUCATION} />
      <SafeAreaView
        style={{
          flex: 1,
          width: '100%',
          justifyContent: 'center',
          alignItems: 'center',
        }}>
        <ScrollView style={{ width: '90%' }}>
          <View
            style={{
              flexDirection: 'row',
              alignItems: 'center',
              marginBottom: 20,
              justifyContent: 'center',
            }}>
            <CommonCheckbox
              onClick={() => setShowEducationButton(!showEducationButton)}
              isChecked={showEducationButton}
              label={strings.Education.IDONTHAVEDEGREE}
            />
          </View>

          {education.map((item, index) => (
            <View
              key={item.id}
              style={[
                BaseStyle.card,
                styles.lightBackgroundShadow,
                {
                  alignContent: 'flex-start',
                  alignItems: 'flex-start',
                  padding: '5%',
                },
              ]}>
              <Text
                style={[
                  styles.tutionClassCardItem,
                  {
                    fontWeight: 'bold',
                    fontSize: 18,
                    marginBottom: 10,
                  },
                ]}>
                #{index + 1}
              </Text>
              <Text style={styles.prolfileItem}>
                {strings.Education.UNIVERSITY} : <Text style={{ fontWeight: 'bold' }}>{item.university || 'N/A'}</Text>
              </Text>
              <Text style={styles.prolfileItem}>
                {strings.Education.DEGREE} : <Text style={{ fontWeight: 'bold' }}>{item.degree || 'N/A'}</Text>
              </Text>
              <Text style={styles.prolfileItem}>
                {strings.Education.DEGREETYPE} : <Text style={{ fontWeight: 'bold' }}>{item.degreeType || 'N/A'}</Text>
              </Text>
              <Text style={styles.prolfileItem}>
                {strings.Education.PASSOUTYEAR} : <Text style={{ fontWeight: 'bold' }}>{item.passoutYear || 'N/A'}</Text>
              </Text>

              <TouchableOpacity
                style={{
                  marginTop: 10,
                  paddingVertical: 6,
                  paddingHorizontal: 12,
                  backgroundColor: PrimaryColors.RED,
                  borderRadius: 6,
                  alignSelf: 'flex-start',
                }}>
                <Text style={{ color: PrimaryColors.WHITE, fontWeight: 'bold' }}>
                  {strings.TutionClass.DELETE}
                </Text>
              </TouchableOpacity>
            </View>
          ))}

          {!showEducationButton && (
            <>
              {educationForms.map(id => (
                <EducationForm
                  key={id}
                  id={id}
                  showRemove={educationForms.length > 1}
                  removeForm={() => {
                    setEducationForms(prev =>
                      prev.filter(formId => formId !== id)
                    );
                  }}
                />
              ))}
              <View
                style={{
                  flexDirection: 'row',
                  justifyContent: 'center',
                  alignItems: 'center',
                  marginBottom: 20,
                }}>
                <TouchableOpacity
                  onPress={addEducationForm}
                  style={[
                    BaseStyle.button,
                    styles.lightBackgroundShadow,
                    { marginTop: 20, width: '50%' },
                  ]}>
                  <Text style={[styles.mainTextColor, { fontSize: 15 }]}>
                    {strings.Education.ADDNEWEDUCATION}
                  </Text>
                </TouchableOpacity>
              </View>
            </>
          )}

          <View style={{ justifyContent: 'center', alignItems: 'center' }}>
            <Button
              title={strings.Education.SAVEEDUCATION}
              onPress={() => console.log()}
            />
          </View>
        </ScrollView>
      </SafeAreaView>
    </SafeAreaProvider>
  );
};

export default Education;

const BaseStyle = StyleSheet.create({
  button: {
    flexDirection: 'row',
    paddingVertical: 12,
    paddingHorizontal: 20,
    borderRadius: 10,
    alignItems: 'center',
    justifyContent: 'center',
  },
  card: {
    width: '100%',
    borderRadius: 12,
    marginTop: 20,
    justifyContent: 'center',
    alignItems: 'center',
    paddingTop: 10,
  },
});
