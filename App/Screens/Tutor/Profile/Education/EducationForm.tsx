import React, { useState } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Image } from 'react-native';
import { IMAGE_CONSTANT, PrimaryColors } from '../../../../Utils/Constants';
import CommonTextInput from '../../../../CommonComponents/CommonTextInput';
import IndexStyle from '../../../../Theme/IndexStyle';
import CommonFileUpload from '../../../../CommonComponents/CommonFileUpload';
import DocumentPicker from '@react-native-documents/picker';
import strings from '../../../../Utils/LocalizedStrings/English';
import CommonDropdownPicker from '../../../../CommonComponents/CommonDropdownPicker';


type Props = {
  id: number;
  removeForm: () => void;
  showRemove: boolean;
};
const EducationForm = ({ id, removeForm, showRemove }: Props) => {
  const [university, setUniversity] = useState('');
  const [degree, setDegree] = useState('');
  const [degreeType, setDegreeType] = useState('');
  const [passoutYear, setPassoutYear] = useState('');
  const [degreeFile, setDegreeFile] = useState<DocumentPicker.DocumentPickerResponse | null>(null);

  const { styles, isDarkMode } = IndexStyle();

  const degrees = [
    { label: 'B.Sc Mathematics', value: 'B.Sc Mathematics' },
    { label: 'B.Sc Computer Science', value: 'B.Sc Computer Science' },
    { label: 'B.Com', value: 'B.Com' },
    { label: 'BBA', value: 'BBA' },
    { label: 'BCA', value: 'BCA' },
    { label: 'BA', value: 'BA' },
    { label: 'M.Sc', value: 'M.Sc' },
    { label: 'M.Com', value: 'M.Com' },
    { label: 'MBA', value: 'MBA' },
    { label: 'MCA', value: 'MCA' },
    { label: 'MA', value: 'MA' },
    { label: 'Ph.D', value: 'Ph.D' },
    { label: 'B.Tech', value: 'B.Tech' },
    { label: 'M.Tech', value: 'M.Tech' },
  ];

  return (
    <View style={[BaseStyle.card, styles.lightBackgroundShadow]}>
      <CommonTextInput
        label={strings.Education.UNIVERSITY}
        placeholder={strings.Education.UNIVERSITYPLACEHOLDER}
        value={university}
        onChangeText={setUniversity}
      />

      <View style={{ width: '80%', marginBottom: '3%' }}>
        <CommonDropdownPicker
          label={strings.Education.DEGREE}
          data={degrees}
          value={degree}
          onChange={setDegree}
          placeholder={strings.Education.SELECTDEGREE}
          style={{ width: '100%' }}
        />
      </View>

      <CommonTextInput
        label={strings.Education.DEGREETYPE}
        placeholder={strings.Education.DEGREETYPEPLACEHOLDER}
        value={degreeType}
        onChangeText={setDegreeType}
      />

      <CommonTextInput
        label={strings.Education.PASSOUTYEAR}
        placeholder={strings.Education.PASSOUTYEARPLACEHOLDER}
        value={passoutYear}
        onChangeText={setPassoutYear}
        keyboardType="numeric"
      />
      <CommonFileUpload label={strings.Education.SELECTDEGREECERTIFICATE} fileName={degreeFile?.name || null} onFileSelect={(file) => setDegreeFile(file)} />

      {showRemove && (
        <View
          style={[{ width: '80%', marginBottom: 20, alignItems: 'flex-start' }]}>
          <TouchableOpacity
            onPress={removeForm}
          >
            {/* <Text style={[{textAlign: 'center'}, styles.mainTextColor]}>
              {strings.Education.REMOVE}
            </Text> */}
            <Image
              resizeMode="contain"
              source={IMAGE_CONSTANT.DELETE}
              style={{
                width: 20,
                height: 20,
              }}
            />

          </TouchableOpacity>
        </View>
      )}
    </View>
  );
};

export default EducationForm;

const BaseStyle = StyleSheet.create({
  card: {
    width: '100%',
    // backgroundColor: '#1e1e1e',
    borderRadius: 12,
    marginTop: 20,
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    paddingTop: 10,
  },
  //   label: {
  //     color: PrimaryColors.BLACK,
  //     fontSize: 14,
  //     marginBottom: 6,
  //     marginTop: 10,
  //   },
  uploadBtn: {
    padding: 10,
    width: '80%',

    borderRadius: 6,
    marginTop: 8,
    marginBottom: 10,
  },
});
