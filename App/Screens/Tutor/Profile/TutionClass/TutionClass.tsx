import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, ScrollView, Alert } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { IMAGE_CONSTANT, PrimaryColors } from '../../../../Utils/Constants';
import { useNavigation } from '@react-navigation/native';
import { SafeAreaView, SafeAreaProvider } from 'react-native-safe-area-context';
import { useSelector } from 'react-redux';
import { RootState } from '../../../../Redux/store';
import Header from '../../../../CommonComponents/Header';
import strings from '../../../../Utils/LocalizedStrings/LocalizedStrings';
import AddTuitionClassModal from '../../../Student/ClassList/ClassFilter';
import IndexStyle from '../../../../Theme/IndexStyle';
import Button from '../../../../CommonComponents/Button';
import api from '../../../../config/api';
import Toast from 'react-native-simple-toast';



const TutionClass = () => {
      const user = useSelector((state: RootState) => state.user);
      const classId=user.userData?.id      
    
    type TuitionItem = {
        education: string;
        board: string;
        medium: string;
        section: string;
        subject: string;
        coachingType: string;
      };
    const navigation = useNavigation();
    const { styles, isDarkMode } = IndexStyle();

    const [modalVisible, setModalVisible] = useState(false);
    // const [tuitionList, setTuitionList] = useState([]);
    const [tutionClasses, setTutionClasses] = useState<any[]>([]);
    const [tuitionList, setTuitionList] = useState<TuitionItem[]>([]);

    const [tuitionForm, setTuitionForm] = useState({
        category: '',
        boardType: '',
        medium: '',
        section: '',
        subject: '',
        coachingType: '',
    });

    useEffect(() => {
        // const loadData = async () => {
        //     const savedList = await AsyncStorage.getItem('tuitionList');
        //     if (savedList) {
        //         setTuitionList(JSON.parse(savedList));
        //     }
        // };
        // loadData();
        fetchTutionClassesData();
    }, []);

    const fetchTutionClassesData = async () => {
        try {
            const response = await api.GetProfileData.getProfileData(
                'efd64e13-b4ec-4a28-9805-08a6b80c2042'
            );

            if (response.ok) {
                const text = await response.text();
                const jsonData = JSON.parse(text);
                const tutionClassData = jsonData.tuitionClasses || [];
                setTutionClasses(tutionClassData);
                console.log("PARSED  DATA::", jsonData);
                console.log("Tution Class DATA::", tutionClassData);
            } else {
                JSON
                console.log('Failed to fetch data. Status:', response.status);
            }
        } catch (err) {
            console.log('ERROR IN GET TUTION CLASSES DATA::', err);
        }
    };

    const parseArrayString = (str:any) => {
        try {
            const arr = JSON.parse(str);
            if (Array.isArray(arr)) return arr.join(', ');
            return str;
        } catch (e) {
            return str;
        }
    };



    const handleAddClass = (formValues: typeof tuitionForm) => {
        setTuitionForm(formValues);
        const updatedList = [...tuitionList, {
            education: formValues.category, // ✅ Renamed from "category"
            board: formValues.boardType,
            medium: formValues.medium,
            section: formValues.section,
            subject: formValues.subject,
            coachingType: formValues.coachingType,
        }];
        setTuitionList(updatedList);
        setModalVisible(false);
    };

    const handleSaveData = async () => {
        try {
            console.log("Hello from save data function");
            
            const response = await api.TutorProfile.TutorTuitionClass.saveTutionList({ tuitionDetails: tuitionList });
            if(response.ok)
            {
                console.log("Tuition data save successfully");
                Toast.show(strings.TutionClass.TUITIONDATASAVEDMESSAGE,Toast.SHORT)
                setTuitionList([]);
                
            }
            else{
                console.log(response);
                
                const errorText = await response.text();
                console.error('Failed to save tuition data:', errorText);
                Toast.show(strings.TutionClass.TUITIONDATASAVEDERROR,Toast.SHORT)


            }

            // Alert.alert(strings.TutionClass.TUITIONDATASAVEDMESSAGE);
        } catch (error) {
            console.error('Tuition class Save error:', error);
            Toast.show(strings.TutionClass.TUITIONDATASAVEDERROR,Toast.SHORT)
        }
    };

    // const handleDeleteCard = (indexToDelete: number) => {
    //     Alert.alert(
    //         strings.TutionClass.CONFIRMDELETE,
    //         strings.TutionClass.DELETEASKINGSTRING,
    //         [
    //             { text: strings.ALL.CANCEL, style: 'cancel' },
    //             {
    //                 text: strings.ALL.DELETE,
    //                 style: 'destructive',
    //                 onPress: () => {
    //                     const updatedList = tuitionList.filter((_, i) => i !== indexToDelete);
    //                     setTuitionList(updatedList);
    //                 }
    //             }
    //         ]
    //     );
    // };

    const handleDeleteTutionClass = (itemId:string,classId: string) => {
        Alert.alert(
          strings.TutionClass.CONFIRMDELETE,
          strings.TutionClass.DELETEASKINGSTRING,
          [
            { text: strings.ALL.CANCEL, style: 'cancel' },
            {
              text: strings.ALL.DELETE,
              style: 'destructive',
              onPress: async () => {
                try {
                  const response = await api.TutorProfile.TutorTuitionClass.deleteTutionClass(itemId,classId);
                  if (response.ok) {
                    Toast.show(strings.TutionClass.TUITIONDELETESUCCESS,Toast.SHORT)

                    fetchTutionClassesData(); // refresh list
                  } else {
                    const errorText = await response.text();
                    console.error('Failed to delete tuition class:', errorText);
                    Toast.show(strings.TutionClass.TUITIONCLASSFAIL,Toast.SHORT)

                  }
                } catch (error) {
                  console.error('Delete error:', error);
                  Toast.show(strings.TutionClass.TUITIONCLASSERROR,Toast.SHORT)

                }
              },
            },
          ]
        );
      };
      
    return (
        <SafeAreaProvider style={styles.container}>
            <Header title={strings.TutionClass.TUTIONCLASS} />
            <SafeAreaView style={{ flex: 1, width: '100%', paddingHorizontal: '5%' }}>
                <ScrollView>
                    {/* <View style={{ padding: 10, margin: 10 }}>
                        {(
                            tuitionList.map((item, index) => (
                                <View key={index} style={[styles.tutionClassCard, { marginBottom: 20 }]}>
                                    <Text style={[styles.tutionClassCardItem, {
                                        fontWeight: 'bold',
                                        fontSize: 18,
                                        marginBottom: 10,
                                    }]}>
                                        #{index + 1}
                                    </Text>
                                    <Text style={styles.tutionClassCardItem}>Category: {item.education}</Text>
                                    <Text style={styles.tutionClassCardItem}>Board: {item.board}</Text>
                                    <Text style={styles.tutionClassCardItem}>Medium: {item.medium}</Text>
                                    <Text style={styles.tutionClassCardItem}>Section: {item.section}</Text>
                                    <Text style={styles.tutionClassCardItem}>Subject: {item.subject}</Text>
                                    <Text style={styles.tutionClassCardItem}>Coaching Type: {item.coachingType}</Text>

                                    <TouchableOpacity
                                        style={{
                                            marginTop: 10,
                                            paddingVertical: 6,
                                            paddingHorizontal: 12,
                                            backgroundColor: PrimaryColors.RED,
                                            borderRadius: 6,
                                            alignSelf: 'flex-start'
                                        }}
                                        onPress={() => handleDeleteCard(index)}
                                    >
                                        <Text style={{ color: PrimaryColors.WHITE, fontWeight: 'bold' }}>{strings.TutionClass.DELETE}</Text>
                                    </TouchableOpacity>
                                </View>
                            ))
                        )}
                    </View> */}

                    <View style={{ marginStart: 20, marginBottom: 10 }}>
                        <TouchableOpacity
                            style={{
                                borderRadius: 10,
                                paddingVertical: 10,
                                paddingHorizontal: 10,
                                width: '50%',
                                borderWidth: 1,
                                borderColor: isDarkMode ? PrimaryColors.WHITE : PrimaryColors.BLACK,
                            }}
                            onPress={() => setModalVisible(true)}
                        >
                            <Text style={{
                                color: isDarkMode ? PrimaryColors.WHITE : PrimaryColors.BLACK,
                                fontWeight: '600',
                                fontSize: 16
                            }}>
                                ➕ {strings.TutionClass.ADDANOTHERTUTION}
                            </Text>
                        </TouchableOpacity>
                    </View>

                    {tutionClasses.map((item, index) => (
                        <View
                            key={item.id}
                            style={[
                                BaseStyle.card,
                                styles.lightBackgroundShadow,
                                {
                                    alignContent: 'flex-start',
                                    alignItems: 'flex-start',
                                    padding: '5%',
                                },
                            ]}>
                            <Text
                                style={[
                                    styles.tutionClassCardItem,
                                    {
                                        fontWeight: 'bold',
                                        fontSize: 18,
                                        marginBottom: 10,
                                    },
                                ]}>
                                #{index + 1}
                            </Text>
                            <Text style={styles.prolfileItem}>
                                {strings.TutionClass.CATEGORY} :{' '}
                                <Text style={{ fontWeight: 'bold' }}>{parseArrayString(item.education) || 'N/A'}</Text>
                            </Text>
                            <Text style={styles.prolfileItem}>
                                {strings.TutionClass.BOARDTYPE} :{' '}
                                <Text style={{ fontWeight: 'bold' }}>{parseArrayString(item.boardType) || 'N/A'}</Text>
                            </Text>

                            <Text style={styles.prolfileItem}>
                                {strings.TutionClass.SUBJECT} :{' '}
                                <Text style={{ fontWeight: 'bold' }}>{parseArrayString(item.subject) || 'N/A'}</Text>
                            </Text>

                            <Text style={styles.prolfileItem}>
                                {strings.TutionClass.MEDIUM} :{' '}
                                <Text style={{ fontWeight: 'bold' }}>{parseArrayString(item.medium) || 'N/A'}</Text>
                            </Text>

                            <Text style={styles.prolfileItem}>
                                {strings.TutionClass.SECTION} :{' '}
                                <Text style={{ fontWeight: 'bold' }}>{parseArrayString(item.section) || 'N/A'}</Text>
                            </Text>

                            <Text style={styles.prolfileItem}>
                                {strings.TutionClass.COASHINGTYPE} :{' '}
                                <Text style={{ fontWeight: 'bold' }}>{parseArrayString(item.coachingType) || 'N/A'}</Text>
                            </Text>

                            <Text style={styles.prolfileItem}>
                                {strings.TutionClass.DETAILS} :{' '}
                                <Text style={{ fontWeight: 'bold' }}>{parseArrayString(item.details) || 'N/A'}</Text>
                            </Text>

                            <TouchableOpacity
                                style={{
                                    marginTop: 10,
                                    paddingVertical: 6,
                                    paddingHorizontal: 12,
                                    backgroundColor: PrimaryColors.RED,
                                    borderRadius: 6,
                                    alignSelf: 'flex-start',
                                }}
                                onPress={() => handleDeleteTutionClass(item.id,classId)} // ← Call API delete handler
>
                                <Text style={{ color: PrimaryColors.WHITE, fontWeight: 'bold' }}>
                                    {strings.TutionClass.DELETE}
                                </Text>
                            </TouchableOpacity>
                        </View>
                    ))}

                    <View style={{ alignItems: 'center', marginBottom: 30 }}>
                        <Button title={strings.TutionClass.SAVETUITIONCLASSLIST} onPress={handleSaveData} />
                    </View>

                    {/* {Object.entries(tuitionForm).map(([key, value]) => (
                        value ? (
                            <Text key={key}>
                                {`${key.charAt(0).toUpperCase() + key.slice(1)}: ${value}`}
                            </Text>
                        ) : null
                    ))} */}
                </ScrollView>

                <AddTuitionClassModal
                    title={strings.TutionClass.ADD}
                    applyLabel={strings.TutionClass.SUBMIT}
                    modalVisible={modalVisible}
                    setModalVisible={setModalVisible}
                    currentFilters={tuitionForm}
                    onApply={handleAddClass}
                    onCancel={() => setModalVisible(false)}
                />
            </SafeAreaView>
        </SafeAreaProvider>
    );
};

export default TutionClass;
const BaseStyle = StyleSheet.create({
    card: {
        width: '100%',
        borderRadius: 12,
        marginTop: 20,
        justifyContent: 'center',
        alignItems: 'center',
        paddingTop: 10,
    },
});