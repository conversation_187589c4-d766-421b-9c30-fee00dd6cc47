import React, { useState } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, ScrollView, FlatList, Image } from 'react-native';
import { IMAGE_CONSTANT, PrimaryColors } from '../../../Utils/Constants';
import { useNavigation } from '@react-navigation/native';
import { SafeAreaView, SafeAreaProvider } from 'react-native-safe-area-context';
import Ionicons from 'react-native-vector-icons/Ionicons';
import Header from '../../../CommonComponents/Header';
import strings from '../../../Utils/LocalizedStrings/LocalizedStrings';
import IndexStyle from '../../../Theme/IndexStyle';
const TutorProfile = () => {
  const navigation = useNavigation<any>();
  const { styles, isDarkMode } = IndexStyle()
  const [collapseStates, setCollapseStates] = useState({
    education: true,
    work: true,
    certificates: true,
    tuition: true,
  });
  const toggleSection = (section: keyof typeof collapseStates) => {
    setCollapseStates(prev => ({
      ...prev,
      [section]: !prev[section],
    }));
  };

  const navItems = [
    { screen: 'About', image: IMAGE_CONSTANT.ABOUT, navigation: "About" },
    { screen: 'Description', image: IMAGE_CONSTANT.DESCRIPTION, navigation: "Description" },
    { screen: 'Photo & Logo', image: IMAGE_CONSTANT.PHOTOFRAME, navigation: "PhotoAndLogo" },
    { screen: 'Education', image: IMAGE_CONSTANT.GRADUATION, navigation: "Education" },
    { screen: 'Experience', image: IMAGE_CONSTANT.EXPERIENCE, navigation: "Experience" },
    { screen: 'Certificate', image: IMAGE_CONSTANT.MEDAL, navigation: "Certificate" },
    { screen: 'Tution Class', image: IMAGE_CONSTANT.TUITIONCLASS, navigation: "TutionClass" },
  ];


  const renderItem = ({ item }) => (
    <TouchableOpacity
      style={[BaseStyle.cardContainer, styles.lightBackgroundShadow]}
      onPress={() => navigation.navigate(item.navigation)}
    >
      <View style={{ width: '8%', alignItems: 'center', marginRight: '4%' }}>
        <Image
          resizeMode="contain"
          source={item.image}
          style={BaseStyle.profileImage}
        />
      </View>
      <View style={{ width: '80%' }}>
        <Text style={[{ fontSize: 16, fontWeight: '400' }, styles.mainTextColor]}>{item.screen}</Text>
      </View>
      <View style={{ width: '8%', alignItems: 'flex-end', }}>
        <Ionicons name="chevron-forward-outline" size={24} color={isDarkMode ? PrimaryColors.WHITE : PrimaryColors.BLACK} />
      </View>
    </TouchableOpacity>
  );
  return (
    <SafeAreaProvider style={styles.container}>
      <Header title={strings.Profile.PROFILE} />
      <SafeAreaView style={[{ flex: 1, width: '100%', paddingHorizontal: '20%' }]}>
        <FlatList
          data={navItems}
          keyExtractor={(item) => item.screen}
          renderItem={renderItem}
        />
      </SafeAreaView>
    </SafeAreaProvider>
  );
};

export default TutorProfile;


const BaseStyle = StyleSheet.create({
  cardContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderRadius: 16,
    padding: 12,
    marginBottom: 16,
  },
  arrowWrapper: {
    width: '10%',
    alignItems: 'flex-end',
  },
  profileImage: {
    width: 30,
    height: 30,
  }
});
