import React, { useEffect, useState } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, ScrollView, FlatList } from 'react-native';
import { IMAGE_CONSTANT, PrimaryColors } from '../../../../Utils/Constants';
import { useNavigation } from '@react-navigation/native';
import LinearGradient from 'react-native-linear-gradient';
import { SafeAreaView, SafeAreaProvider } from 'react-native-safe-area-context';

import Ionicons from 'react-native-vector-icons/Ionicons';
import Header from '../../../../CommonComponents/Header';
import strings from '../../../../Utils/LocalizedStrings/LocalizedStrings';
import CommonTextInput from '../../../../CommonComponents/CommonTextInput';
import Button from '../../../../CommonComponents/Button';
import IndexStyle from '../../../../Theme/IndexStyle';
import AsyncStorage from '@react-native-async-storage/async-storage';
import api from '../../../../config/api';
import { useSelector } from 'react-redux';
import Toast from 'react-native-simple-toast';


const Description = () => {
      const user=useSelector((state=>state.user))
    
    const navigation = useNavigation();
    const { styles, isDarkMode } = IndexStyle();

    const [headline, setHeadline] = useState('');
    const [bio, setBio] = useState('');
    const [formSubmitted, setFormSubmitted] = useState(false);

    const handleUpdateProfile = async() => {
        setFormSubmitted(true);

        if (!headline.trim() || !bio.trim()) {
            return;  // Prevent submission if fields are empty
        }
        else{
                try {
                  const payload = {
                    classId: user.userData.id,
                    headline,
                    bio,
                  };
              
                  console.log("Sending update payload:", payload);
              
                  const response = await api.TutorProfile.TutorDescription.updateAbout(payload);
              
                  if (response.ok) {
                    const resultText = await response.text();
                    console.log("Update success:", resultText);
                    Toast.show("Profile Updated Successfully", Toast.SHORT);
                    // navigation.navigate('Description')
                    // Optionally show a toast or navigate
                  }
                  else if(response.status===400)
                  {
                    console.log(response);
                    
                    Toast.show("Email And UserName should be unique",Toast.SHORT)
                  }
                   else {
                    console.log("Update failed. Status:", response);
                  }
                } catch (error) {
                  console.log("Error while updating profile:", error);
              };
              
        }

        // Perform the actual update logic here
        console.log('Profile Updated:', { headline, bio });
    };

    useEffect(() => {
        fetchDescriptionData();
    }, []);
    const userId=user.userData.id

    const fetchDescriptionData = async () => {
        try {
            // const classId = await AsyncStorage.getItem('classId');

            const response = await api.GetProfileData.getProfileData(userId);

            if (response.ok) {
                const text = await response.text();
                console.log("RAW RESPONSE TEXT::", text);

                const jsonData = JSON.parse(text);
                console.log("PARSED JSON DATA::", jsonData);

                setHeadline(jsonData.ClassAbout.catchyHeadline || '');
                setBio(jsonData.ClassAbout.tutorBio || '');

            } else {
                console.log("Failed to Description data. Status:", response.status);
            }
        } catch (err) {
            console.log("ERROR IN GET DESCRIPTION DATA::", err);
        }
    };

    return (
        <SafeAreaProvider style={styles.container}>
            <Header title={strings.Description.DESCRIPTION} />
            <SafeAreaView style={{ flex: 1, width: '100%', alignItems: 'center' }}>
                <CommonTextInput
                    label={strings.Description.CATCHYHEADLINE}
                    placeholder={strings.Description.HEADLINEPLACEHOLDER}
                    value={headline}
                    onChangeText={setHeadline}
                />
                {formSubmitted && !headline.trim() && (
                    <Text style={{ color: PrimaryColors.RED, fontSize: 12, marginBottom: '2%' }}>
                        {strings.Description.CATCHYHEADLINE} {strings.Description.ISREQUIRED}
                    </Text>
                )}

                <CommonTextInput
                    label={strings.Description.TUTORBIO}
                    placeholder={strings.Description.BIOPLACEHOLDER}
                    value={bio}
                    onChangeText={setBio}
                    multiline
                />
                {formSubmitted && !bio.trim() && (
                    <Text style={{ color: PrimaryColors.RED, fontSize: 12, marginBottom: '2%' }}>
                        {strings.Description.TUTORBIO} {strings.Description.ISREQUIRED}
                    </Text>
                )}

                <Button title={strings.Description.SAVEPROFILE} onPress={handleUpdateProfile} />
            </SafeAreaView>
        </SafeAreaProvider>

    );
};

export default Description;

const LightStyles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: PrimaryColors.WHITE,
        alignItems: 'center',
    },
    lightHeader: {
        height: 70,
        width: '100%',
        borderBottomLeftRadius: 60,
        borderBottomRightRadius: 60,
        alignItems: 'center',
        justifyContent: 'center',
    },

});

const DarkStyles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: PrimaryColors.LIGHTGRAY,
        alignItems: 'center',
    },
    lightHeader: {
        height: 70,
        width: '100%',
        borderBottomLeftRadius: 60,
        borderBottomRightRadius: 60,
        alignItems: 'center',
        justifyContent: 'center',
    },

});
