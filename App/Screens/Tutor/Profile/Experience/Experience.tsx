import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { SafeAreaView, SafeAreaProvider } from 'react-native-safe-area-context';
import Header from '../../../../CommonComponents/Header';
import strings from '../../../../Utils/LocalizedStrings/LocalizedStrings';
import IndexStyle from '../../../../Theme/IndexStyle';
import ExperienceForm from './ExperienceForm';
import Button from '../../../../CommonComponents/Button';
import CommonCheckbox from '../../../../CommonComponents/CommonCheckbox';
import api from '../../../../config/api';
import { PrimaryColors } from '../../../../Utils/Constants';

const Experience = () => {
  const navigation = useNavigation();
  const { styles, isDarkMode } = IndexStyle();

  const [showExperienceButton, setShowExperienceButton] = useState(false);
  const [experienceForms, setExperienceForms] = useState<number[]>([]);
  const [experience, setExperience] = useState<any[]>([]);

  const addExperienceForm = () => {
    setExperienceForms(prev => [...prev, Date.now()]);
  };
  const removeForm = (id: number) => {
    setExperienceForms(prev => prev.filter(formId => formId !== id));
  };

  useEffect(() => {
    fetchExperienceData();
  }, []);

  const fetchExperienceData = async () => {
    try {
      const response = await api.GetProfileData.getProfileData(
        'efd64e13-b4ec-4a28-9805-08a6b80c2042'
      );

      if (response.ok) {
        const text = await response.text();
        const jsonData = JSON.parse(text);
        const experienceData = jsonData.experience || [];

        const formattedExperience = experienceData.map((exp: any) => ({
          ...exp,
          from: formatDate(exp.from),
          to: formatDate(exp.to),
        }));

        setExperience(formattedExperience);
        console.log("PARSED DATA::", formattedExperience);
      } else {
        console.log('Failed to fetch data. Status:', response.status);
      }
    } catch (err) {
      console.log('ERROR IN GET EXPERIENCE DATA::', err);
    }
  };

  const formatDate = (dateStr: string) => {
    if (!dateStr || dateStr.toLowerCase() === 'present') return 'Present';
    const date = new Date(dateStr);
    if (isNaN(date.getTime())) return 'Invalid Date';

    const day = date.getDate().toString().padStart(2, '0'); 
    const month = date.toLocaleString('en-US', { month: 'short' });
    const year = date.getFullYear(); 

    return `${day} ${month}, ${year}`; 
  };


  return (
    <SafeAreaProvider style={styles.container}>
      <Header title={strings.Experience.EXPERIENCE} />
      <SafeAreaView
        style={{
          flex: 1,
          width: '100%',
          justifyContent: 'center',
          alignItems: 'center',
        }}>
        <ScrollView style={{ width: '90%' }}>
          <View
            style={{
              flexDirection: 'row',
              alignItems: 'center',
              marginBottom: 20,
              display: 'flex',
              justifyContent: 'center',
            }}>
            <CommonCheckbox
              onClick={() => setShowExperienceButton(!showExperienceButton)}
              isChecked={showExperienceButton}
              label={strings.Experience.IDONTHAVEEXPERIENCE}
            />
          </View>

          {experience.map((item, index) => (
            <View
              key={item.id}
              style={[
                BaseStyle.card,
                styles.lightBackgroundShadow,
                {
                  alignContent: 'flex-start',
                  alignItems: 'flex-start',
                  padding: '5%',
                },
              ]}>
              <Text
                style={[
                  styles.tutionClassCardItem,
                  {
                    fontWeight: 'bold',
                    fontSize: 18,
                    marginBottom: 10,
                  },
                ]}>
                #{index + 1}
              </Text>
              <Text style={styles.prolfileItem}>
                {strings.Experience.TITLE} : <Text style={{ fontWeight: 'bold' }}>{item.title || 'N/A'}</Text>
              </Text>
              <Text style={styles.prolfileItem}>
                {strings.Experience.FROM} : <Text style={{ fontWeight: 'bold' }}>{item.from || 'N/A'}</Text>
              </Text>
              <Text style={styles.prolfileItem}>
                {strings.Experience.TO} : <Text style={{ fontWeight: 'bold' }}>{item.to || 'N/A'}</Text>
              </Text>

              <TouchableOpacity
                style={{
                  marginTop: 10,
                  paddingVertical: 6,
                  paddingHorizontal: 12,
                  backgroundColor: PrimaryColors.RED,
                  borderRadius: 6,
                  alignSelf: 'flex-start',
                }}>
                <Text style={{ color: PrimaryColors.WHITE, fontWeight: 'bold' }}>
                  {strings.TutionClass.DELETE}
                </Text>
              </TouchableOpacity>
            </View>
          ))}

          {!showExperienceButton && (
            <>
              {experienceForms.map(id => (
                <ExperienceForm
                  key={id}
                  id={id}
                  showRemove={experienceForms.length > 1}
                  removeForm={() => removeForm(id)}
                />
              ))}
              <View
                style={{
                  flexDirection: 'row',
                  justifyContent: 'center',
                  alignItems: 'center',
                  marginBottom: 20,
                }}>
                <TouchableOpacity
                  onPress={addExperienceForm}
                  style={[
                    BaseStyle.button,
                    styles.lightBackgroundShadow,
                    { marginTop: 20, width: '50%' },
                  ]}>
                  <Text style={[styles.mainTextColor]}>
                    {strings.Experience.ADDMOREEXPERIENCE}
                  </Text>
                </TouchableOpacity>
              </View>
            </>
          )}

          <View style={{ justifyContent: 'center', alignItems: 'center' }}>
            <Button title={strings.Experience.SAVEEXPERIENCE} onPress={() => console.log()} />
          </View>
        </ScrollView>
      </SafeAreaView>
    </SafeAreaProvider>
  );
};

export default Experience;

const BaseStyle = StyleSheet.create({
  button: {
    flexDirection: 'row',
    paddingVertical: 12,
    paddingHorizontal: 20,
    borderRadius: 10,
    alignItems: 'center',
    justifyContent: 'center',
  },
  card: {
    width: '100%',
    borderRadius: 12,
    marginTop: 20,
    justifyContent: 'center',
    alignItems: 'center',
    paddingTop: 10,
  },
});
