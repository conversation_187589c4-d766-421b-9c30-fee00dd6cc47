import React, { useState } from 'react';
import { View, StyleSheet, TouchableOpacity, Image } from 'react-native';
import CommonTextInput from '../../../../CommonComponents/CommonTextInput';
import CommonFileUpload from '../../../../CommonComponents/CommonFileUpload';
import CommonDateTimePicker from '../../../../CommonComponents/CommonDateTimePicker';
import IndexStyle from '../../../../Theme/IndexStyle';
import { IMAGE_CONSTANT } from '../../../../Utils/Constants';
import DocumentPicker from '@react-native-documents/picker';
import strings from '../../../../Utils/LocalizedStrings/LocalizedStrings';

type Props = {
  id: number;
  removeForm: () => void;
  showRemove: boolean;
};

const ExperienceForm = ({ id, removeForm, showRemove }: Props) => {
  const [title, setTitle] = useState('');
  const [fromDate, setFromDate] = useState<Date | null>(null);
  const [toDate, setToDate] = useState<Date | null>(null);
  const [proofFile, setProofFile] = useState<DocumentPicker.DocumentPickerResponse | null>(null);

  const { styles } = IndexStyle();

  return (
    <View style={[BaseStyle.card, styles.lightBackgroundShadow]}>
      <CommonTextInput
        label={strings.Experience.EXPERIENCETITLE}
        placeholder={strings.Experience.EXPERIENCETITLEPLACEHOLDER}
        value={title}
        onChangeText={setTitle}
      />

      <CommonFileUpload
        label={strings.Experience.UPLOADEXPERIENCEPROOF}
        fileName={proofFile?.name || null}
        onFileSelect={setProofFile}
      />

      <CommonDateTimePicker
        label={strings.Experience.FROM}
        innerText={strings.Experience.SELECTSTARTDATE}
        value={fromDate}
        onChange={setFromDate}
      />

      <CommonDateTimePicker
        label={strings.Experience.TO}
        innerText={strings.Experience.SELECTENDDATE}
        value={toDate}
        onChange={setToDate}
        minimumDate={fromDate || undefined}
      />

      {showRemove && (
        <View style={{ width: '80%', marginBottom: 20, alignItems: 'flex-start' }}>
          <TouchableOpacity onPress={removeForm}>
            <Image
              resizeMode="contain"
              source={IMAGE_CONSTANT.DELETE}
              style={{ width: 20, height: 20 }}
            />
          </TouchableOpacity>
        </View>
      )}
    </View>
  );
};

export default ExperienceForm;

const BaseStyle = StyleSheet.create({
  card: {
    width: '100%',
    borderRadius: 12,
    marginTop: 20,
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    paddingTop: 10,
  },
});
