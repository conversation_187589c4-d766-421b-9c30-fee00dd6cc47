
import { IMAGE_CONSTANT, PrimaryColors } from '../../../../Utils/Constants';
import React, { useState, useEffect, useCallback, memo } from 'react';
import { View, Text, StyleSheet, TouchableOpacity,Image } from 'react-native';
import CommonTextInput from '../../../../CommonComponents/CommonTextInput';
import CommonFileUpload from '../../../../CommonComponents/CommonFileUpload';
import IndexStyle from '../../../../Theme/IndexStyle';
import DocumentPicker from '@react-native-documents/picker';
import strings from '../../../../Utils/LocalizedStrings/English';

type Props = {
  id: number;
  removeForm: () => void;
  showRemove: boolean;
  onChange?: (title: string, file: DocumentPicker.DocumentPickerResponse | null) => void;
};

const CertificateForm = memo(({ id, removeForm, showRemove, onChange }: Props) => {
  const [certificateName, setCertificateName] = useState('');
  const [certificateFile, setCertificateFile] = useState<DocumentPicker.DocumentPickerResponse | null>(null);
  
  const { styles, isDarkMode } = IndexStyle();
  
  // Memoize handlers to prevent unnecessary re-renders
  const handleNameChange = useCallback((text: string) => {
    setCertificateName(text);
  }, []);
  
  const handleFileSelect = useCallback((file: DocumentPicker.DocumentPickerResponse | null) => {
    setCertificateFile(file);
  }, []);
  
  // Only call onChange when values actually change
  useEffect(() => {
    if (onChange) {
      onChange(certificateName, certificateFile);
    }
  }, [certificateName, certificateFile, onChange]);
  
  return (
    <View style={[BaseStyle.card, styles.lightBackgroundShadow]}>
      <CommonTextInput
        label="Certificate Name"
        placeholder="e.g. Teaching Excellence"
        value={certificateName}
        onChangeText={handleNameChange}
      />
      
      <CommonFileUpload
        label={strings.Certificate.UPLOADCERTIFICATE}
        fileName={certificateFile?.name || null}
        onFileSelect={handleFileSelect}
      />
      
      {showRemove && (
        <View style={{ width: '80%', marginBottom: 20, alignItems: 'flex-start' }}>
          <TouchableOpacity onPress={removeForm}>
            <Image
              resizeMode="contain"
              source={IMAGE_CONSTANT.DELETE}
              style={{ width: 20, height: 20 }}
            />
          </TouchableOpacity>
        </View>
      )}
    </View>
  );
});

export default CertificateForm;

const BaseStyle = StyleSheet.create({
  card: {
    width: '100%',
    borderRadius: 12,
    marginTop: 20,
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    paddingTop: 10,
  },
  removeButton: {
    height: 50,
    justifyContent: 'center',
    borderRadius: 5,
    width: '20%',
    borderWidth: 1,
  },
});
