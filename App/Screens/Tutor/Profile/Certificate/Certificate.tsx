import React, { useEffect, useState, useCallback, useMemo } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Alert,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { SafeAreaView, SafeAreaProvider } from 'react-native-safe-area-context';
import Header from '../../../../CommonComponents/Header';
import strings from '../../../../Utils/LocalizedStrings/LocalizedStrings';
import IndexStyle from '../../../../Theme/IndexStyle';
import CertificateForm from './CertificateForm';
import Button from '../../../../CommonComponents/Button';
import CommonCheckbox from '../../../../CommonComponents/CommonCheckbox';
import api from '../../../../config/api';
import { PrimaryColors } from '../../../../Utils/Constants';
import Toast from 'react-native-simple-toast';

const CertificateScreen = () => {
  const navigation = useNavigation();
  const { styles, isDarkMode } = IndexStyle();

  const [showCertificateCheckbox, setShowCertificateCheckbox] = useState(false);
  const [certificateForms, setCertificateForms] = useState<number[]>([]);
  const [certificate, setCertificate] = useState<any[]>([]);
  const [formData, setFormData] = useState<
    { id: number; title: string; file: any }[]
  >([]);

  // Memoize handlers to prevent recreating functions on every render
  const addCertificateForm = useCallback(() => {
    setCertificateForms((prev) => [...prev, Date.now()]);
  }, []);

  const removeForm = useCallback((id: number) => {
    setCertificateForms((prev) => prev.filter((formId) => formId !== id));
    setFormData((prev) => prev.filter((data) => data.id !== id));
  }, []);

  const handleCheckboxToggle = useCallback(() => {
    setShowCertificateCheckbox((prev) => {
      const newValue = !prev;
      
      if (newValue) {
        setCertificateForms([]);
        setFormData([]);
        setCertificate([]);
        // Move handleSave call outside this function
        setTimeout(() => handleSave(true), 0);
      }
      
      return newValue;
    });
  }, []);

  // Memoized form update function
  const updateFormData = useCallback(
    (id: number, title: string, file: any) => {
      setFormData((prev) => {
        const existing = prev.find((data) => data.id === id);
        if (existing) {
          // Only update if values actually changed
          if (existing.title === title && existing.file === file) {
            return prev;
          }
          return prev.map((data) =>
            data.id === id ? { ...data, title, file } : data
          );
        }
        return [...prev, { id, title, file }];
      });
    },
    []
  );

  const handleSave = useCallback(async (noCertificates: boolean = showCertificateCheckbox) => {
    try {
      if (!noCertificates) {
        if (formData.length === 0) {
          Toast.show('Please add at least one certificate record', Toast.SHORT);
          return;
        }

        const invalidCert = formData.find(
          (data) => !data.title.trim() || !data.file?.uri
        );
        if (invalidCert) {
          Toast.show('All certificates must have a title and a file', Toast.SHORT);
          return;
        }
      }

      const certificates = noCertificates
        ? []
        : formData.map((data) => ({
            title: data.title,
            file: data.file,
          }));

      const response = await api.TutorProfile.TutorCertificate.uploadCertificates(
        noCertificates,
        certificates
      );

      if (response.ok) {
        Toast.show('Certificates saved successfully', Toast.SHORT);
        await fetchCertificateData();
      } else {
        Toast.show(`Failed to save certificates. Status: ${response.status}`, Toast.SHORT);
      }
    } catch (err) {
      console.log('ERROR IN SAVING CERTIFICATES:', err);
      Toast.show('Something went wrong while saving certificates', Toast.SHORT);
    }
  }, [formData, showCertificateCheckbox]);

  const fetchCertificateData = useCallback(async () => {
    try {
      const response = await api.GetProfileData.getProfileData(
        'efd64e13-b4ec-4a28-9805-08a6b80c2042'
      );

      if (response.ok) {
        const text = await response.text();
        const jsonData = JSON.parse(text);
        const certificateData = jsonData.certificates || [];
        setCertificate(certificateData);

        const hasNoCertificates = certificateData.some(
          (cert: any) => cert.isCertificate === false
        );
        setShowCertificateCheckbox(hasNoCertificates);
        if (hasNoCertificates) {
          setCertificateForms([]);
          setFormData([]);
        }
      } else {
        console.log('Failed to fetch data. Status:', response.status);
      }
    } catch (err) {
      console.log('ERROR IN GET CERTIFICATE DATA:', err);
    }
  }, []);

  // Only fetch data on initial mount
  useEffect(() => {
    fetchCertificateData();
  }, []);

  // Memoize the rendering of certificate forms
  const renderCertificateForms = useMemo(() => {
    if (showCertificateCheckbox) return null;
    
    return certificateForms.map((id) => (
      <CertificateForm
        key={id}
        id={id}
        showRemove={certificateForms.length > 1}
        removeForm={() => removeForm(id)}
        onChange={(title, file) => updateFormData(id, title, file)}
      />
    ));
  }, [certificateForms, showCertificateCheckbox, updateFormData, removeForm]);

  // Memoize existing certificates display
  const renderExistingCertificates = useMemo(() => {
    if (showCertificateCheckbox || certificate.length === 0) return null;
    
    return certificate.map((item, index) => (
      <View
        key={item.id}
        style={[
          BaseStyle.card,
          styles.lightBackgroundShadow,
          {
            alignContent: 'flex-start',
            alignItems: 'flex-start',
            padding: '5%',
          },
        ]}>
        <Text
          style={[
            styles.tutionClassCardItem,
            {
              fontWeight: 'bold',
              fontSize: 18,
              marginBottom: 10,
            },
          ]}>
          #{index + 1}
        </Text>
        <Text style={styles.prolfileItem}>
          {strings.Certificate.TITLE} :{' '}
          <Text style={{ fontWeight: 'bold' }}>{item.title || 'N/A'}</Text>
        </Text>
      </View>
    ));
  }, [certificate, showCertificateCheckbox, styles]);

  return (
    <SafeAreaProvider style={styles.container}>
      <Header title={strings.Certificate.CERTIFICATE} />
      <SafeAreaView style={{ flex: 1, width: '100%', alignItems: 'center' }}>
        <ScrollView style={{ width: '90%' }}>
          <View
            style={{
              flexDirection: 'row',
              alignItems: 'center',
              marginBottom: 20,
              justifyContent: 'center',
            }}>
            <CommonCheckbox
              onClick={handleCheckboxToggle}
              isChecked={showCertificateCheckbox}
              label={strings.Certificate.DONOTHAVECERTIFICATE}
            />
          </View>

          {renderExistingCertificates}

          {!showCertificateCheckbox && (
            <>
              {renderCertificateForms}
              <View
                style={{
                  flexDirection: 'row',
                  justifyContent: 'center',
                  alignItems: 'center',
                  marginBottom: 20,
                }}>
                <TouchableOpacity
                  onPress={addCertificateForm}
                  style={[
                    BaseStyle.button,
                    styles.lightBackgroundShadow,
                    { marginTop: 20, width: '50%' },
                  ]}>
                  <Text style={[styles.mainTextColor]}>
                    {strings.Certificate.ADDMORECERTIFICATE}
                  </Text>
                </TouchableOpacity>
              </View>
            </>
          )}

          <View style={{ justifyContent: 'center', alignItems: 'center' }}>
            <Button
              title={strings.Certificate.SAVECERTIFICATES}
              onPress={() => handleSave()}
            />
          </View>
        </ScrollView>
      </SafeAreaView>
    </SafeAreaProvider>
  );
};

export default CertificateScreen;

const BaseStyle = StyleSheet.create({
  button: {
    flexDirection: 'row',
    paddingVertical: 12,
    paddingHorizontal: 20,
    borderRadius: 10,
    alignItems: 'center',
    justifyContent: 'center',
  },
  card: {
    width: '100%',
    borderRadius: 12,
    marginTop: 20,
    justifyContent: 'center',
    alignItems: 'center',
    paddingTop: 10,
  },
});