import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  TextInput,
  StyleSheet,
  TouchableOpacity,
  Image,
  Alert,
} from 'react-native';
import { SafeAreaView, SafeAreaProvider } from 'react-native-safe-area-context';
import Ionicons from 'react-native-vector-icons/Ionicons';
import Header from '../../../CommonComponents/Header';
import strings from '../../../Utils/LocalizedStrings/LocalizedStrings';
import { IMAGE_CONSTANT, PrimaryColors } from '../../../Utils/Constants';
import IndexStyle from '../../../Theme/IndexStyle';
import Button from '../../../CommonComponents/Button';
import api from '../../../config/api';
import Toast from 'react-native-simple-toast';
import { ScrollView } from 'react-native-gesture-handler';
import CommonTextInput from '../../../CommonComponents/CommonTextInput';

const Testimonials = () => {
  const { styles } = IndexStyle();
  const [rating, setRating] = useState(0);
  const [message, setMessage] = useState('');
  const [error, setError] = useState({ rating: false, message: false });
  const [testimonials, setTestimonials] = useState<any[]>([]);

  const handleRatingPress = (star: number) => {
    setRating(star);
    if (star > 0) {
      setError((prev) => ({ ...prev, rating: false }));
    }
  };

  useEffect(() => {
    fetchTestimonialsData();
  }, []);

  const fetchTestimonialsData = async () => {
    try {
      // const classId = await AsyncStorage.getItem('classId');

      const response = await api.GetTestmoinalData.getTestmoinalData('efd64e13-b4ec-4a28-9805-08a6b80c2042');

      if (response.ok) {
        const text = await response.text();
        const jsonData = JSON.parse(text);
        setTestimonials(jsonData || []);
        console.log("PARSED  DATA::", jsonData);
      } else {
        console.log("Failed to Testimonial data. Status:", response.status);
      }
    } catch (err) {
      console.log("ERROR IN GET TESTMONIAL DATA::", err);
    }
  }

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const day = String(date.getDate()).padStart(2, '0');
    const month = String(date.getMonth() + 1).padStart(2, '0'); // Months are 0-based
    const year = date.getFullYear();
    return `${day}/${month}/${year}`;
  };

  const handleSubmit = async () => {
    const messageError = !message.trim();
    const ratingError = rating === 0;

    setError({
      message: messageError,
      rating: ratingError,
    });

    if (messageError || ratingError) {
      return;
    }

    try {
      // const classId = await AsyncStorage.getItem('classId');
      const classId = 'efd64e13-b4ec-4a28-9805-08a6b80c2042';
      const testimonialData = {
        classId,
        rating,
        message,
      };
      const response = await api.AddTestmoinalData.addTestmoinalData(testimonialData);
      if (response.ok) {
        Toast.show(strings.Testmonials.SUCCESSMESSAGE, Toast.SHORT);
        setMessage('');
        setRating(0);
        fetchTestimonialsData();
      } else {
        Toast.show(strings.Testmonials.ERRORMESSAGE, Toast.LONG);
        console.log("Submit failed. Status:", response.status);
      }
    } catch (error) {
      console.log("Error submitting testimonial:", error);
    }
  };


  const handleDelete = (id: string) => {
    console.log("Testimonial ID", id);
    Alert.alert(
      strings.Testmonials.REMOVEFROMTESTMONIAL, strings.Testmonials.CONFIRMDELETE,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: async () => {
            try {
              const response = await api.DeleteTestmoinalData.deleteTestmoinalData(id);
              if (response.ok || response.status === 204) {
                Toast.show(strings.Testmonials.REMOVEDMESSAGE, Toast.SHORT);
                fetchTestimonialsData();
              } else {
                console.log('Failed to delete data. Status:', response.status);
              }
            } catch (err) {
              console.log('ERROR IN DELETE DATA::', err);
            }
          },
        },
      ],
      { cancelable: true }
    );
  };

  const renderStars = () => {
    return (
      <View style={{ flexDirection: 'row', marginVertical: '1%', marginBottom: '5%' }}>
        {[1, 2, 3, 4, 5].map((star) => (
          <TouchableOpacity key={star} onPress={() => handleRatingPress(star)}>
            <Ionicons
              name={star <= rating ? 'star' : 'star-outline'}
              size={24}
              color={PrimaryColors.ORANGE}
              style={{ marginHorizontal: 2 }}
            />
          </TouchableOpacity>
        ))}
      </View>
    );
  };

  const renderStaticStars = (value: number) => (
    <View style={{ flexDirection: 'row', marginVertical: 5 }}>
      {[1, 2, 3, 4, 5].map((star) => (
        <Ionicons
          key={star}
          name={star <= value ? 'star' : 'star-outline'}
          size={20}
          color={PrimaryColors.ORANGE}
          style={{ marginHorizontal: 1 }}
        />
      ))}
    </View>
  );

  return (
    <SafeAreaProvider style={styles.container}>
      <Header title={strings.Testmonials.TESTIMONIALS} />
      <ScrollView>
        <SafeAreaView style={[{ paddingHorizontal: '20%', paddingEnd: '10%', width: '100%' }]}>
          <View style={[styles.testimonialCard, styles.lightBackgroundShadow]}>
            <Text style={[styles.title, styles.mainTextColor, { marginBottom: '2%' }]}>
              {strings.Testmonials.WRITETESTMONIAL}
            </Text>

            <Text style={[styles.label, styles.mainTextColor]}>
              {strings.Testmonials.YOURRATING}
            </Text>
            {renderStars()}
            {error.rating && (
              <Text style={{ color: PrimaryColors.RED, fontSize: 12 }}>
                {strings.Testmonials.RATING} {strings.Testmonials.ISREQUIRED}
              </Text>
            )}

            <CommonTextInput
              label={strings.Testmonials.YOURMESSAGE}
              placeholder={strings.Testmonials.SHAREYOUREXPERIENCE}
              value={message}
              onChangeText={(text) => {
                setMessage(text);
                if (text.trim()) {
                  setError((prev) => ({ ...prev, message: false }));
                }
              }}
              multiline
              style={{ width: '100%' }}
            />
            {error.message && (
              <Text style={{ color: PrimaryColors.RED, fontSize: 12, marginTop: '2%' }}>
                {strings.Testmonials.MESSAGE} {strings.Testmonials.ISREQUIRED}
              </Text>
            )}

            <View style={{ alignItems: 'center', marginTop: 10 }}>
              <Button title={strings.Testmonials.SUBMITTESTIMONIALS} onPress={handleSubmit} />
            </View>
          </View>

          {testimonials.map((item, index) => (
            <View
              key={item.id}
              style={[
                localStyles.card,
                styles.lightBackgroundShadow,
                {
                  alignContent: 'flex-start',
                  alignItems: 'flex-start',
                  padding: '5%',
                },
              ]}>

              <View style={{ flexDirection: 'row' }}>
                <View style={{ width: '95%' }}>
                  <Text style={[localStyles.title, { fontWeight: 'bold', }]}> {item.class.firstName || ''} {item.class.lastName || ''} </Text>
                </View>

                <View
                  style={[{ marginTop: '2%', alignItems: 'flex-start' }]}>
                  <TouchableOpacity
                    onPress={() => handleDelete(item.id)}
                  >
                    <Image
                      resizeMode="contain"
                      source={IMAGE_CONSTANT.DELETE}
                      style={{
                        width: 20,
                        height: 20,
                      }}
                    />
                  </TouchableOpacity>
                </View>
              </View>

              {renderStaticStars(item.rating || 0)}
              <Text style={[localStyles.label, { fontWeight: 'bold' }]}> {item.message || 'N/A'} </Text>
              <View
                style={{
                  flexDirection: 'row',
                  justifyContent: 'space-between',
                  alignItems: 'center',
                }}
              >
                <Text style={localStyles.label}>
                  {strings.Testmonials.POSTEDON}: {formatDate(item.createdAt) || 'N/A'}
                </Text>

                <View
                  style={{
                    backgroundColor:
                      item.status === strings.Testmonials.APPROVED
                        ? PrimaryColors.GREEN
                        : item.status === strings.Testmonials.PANDING
                          ? PrimaryColors.WHITE
                          : PrimaryColors.ORANGE,
                    paddingHorizontal: 10,
                    paddingVertical: 4,
                    borderRadius: 8,
                    marginStart: '37%',
                  }}
                >
                  <Text
                    style={{
                      color:
                        item.status === strings.Testmonials.PANDING
                          ? PrimaryColors.BLACK
                          : PrimaryColors.WHITE,
                      fontSize: 12,
                      fontWeight: '600',
                    }}
                  >
                    {item.status}
                  </Text>
                </View>
              </View>
            </View>
          ))}
        </SafeAreaView>
      </ScrollView>
    </SafeAreaProvider >
  );
};

export default Testimonials;

const localStyles = StyleSheet.create({
  testimonialCard: {
    backgroundColor: '#fff',
    width: '100%',
    borderRadius: 10,
    padding: 15,
    marginTop: 10,
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  label: {
    marginTop: 10,
    fontWeight: '600',
  },
  textInput: {
    borderWidth: 1,
    borderColor: PrimaryColors.GRAYSHADOW,
    borderRadius: 8,
    height: 100,
    padding: 10,
    textAlignVertical: 'top',
    marginTop: 5,
  },
  card: {
    width: '100%',
    borderRadius: 12,
    marginTop: 20,
    justifyContent: 'center',
    alignItems: 'center',
    paddingTop: 10,
  },
});