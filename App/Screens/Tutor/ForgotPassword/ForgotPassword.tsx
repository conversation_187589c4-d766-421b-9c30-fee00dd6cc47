// App/Screens/Login/Login.tsx
import React, { useEffect, useState } from 'react';
import { View, Text, StyleSheet, Image, TouchableOpacity } from 'react-native';
import { TextInput } from 'react-native-gesture-handler';
import LinearGradient from 'react-native-linear-gradient';
import { IMAGE_CONSTANT, PrimaryColors } from '../../../Utils/Constants';
import { useNavigation } from '@react-navigation/native';
import strings from '../../../Utils/LocalizedStrings/LocalizedStrings';
import api from '../../../config/api';

import HeaderWithLogo from '../../../CommonComponents/HeaderWithLogo';
import IndexStyle from '../../../Theme/IndexStyle';
import Button from '../../../CommonComponents/Button';
import Toast from 'react-native-simple-toast'
import CommonTextInput from '../../../CommonComponents/CommonTextInput';

const TutorLogin = () => {
  const navigation = useNavigation<any>();
  const { styles, isDarkMode } = IndexStyle();
  const [userName, setUserName] = useState('');
  const [isusername, setIsusername] = useState(false);

  useEffect(() => {
    SendResetLink();
  })
  const SendResetLink = () => {
    console.log(
      'HERE WE HAVE TO ADD CODE FOR EMAIL SENT AND API CALLING FOR SENT LINK VIA REGISTERED MAIL',
    );
    if (userName == "") {
      setIsusername(true);
    }
    else {
      var data = {
        email: userName
      }
      api.ForgotPassword.forgotPassword(data).then(async (res) => {
        const resData = await res.json();
        console.log("IN FORGOT RESPONSE:::", resData, res.status);
        if (res.status === 200) {
          navigation.navigate('TutorLogin');
        }
        else if (res.status === 400 || res.status === 401) {
          Toast.show(resData.message, Toast.SHORT)
        }
        else if (res.status === 404) {
          Toast.show(resData.message, Toast.SHORT)
        }
        else {
          Toast.show(strings.ForgotPassword.SOMETHINGWENTWRONG, Toast.SHORT)
        }
      }).catch((err) => {
        console.log("IN FORGOT RESPONSE ERROR:::", err);
      })

    }
  };

  return (
    <View style={styles.container}>
      <HeaderWithLogo />

      <View style={{ width: '80%', marginBottom: 20 }}>
        <CommonTextInput
          label={strings.ForgotPassword.EMAIL}
          placeholder={strings.ForgotPassword.ENTEREMAIL}
          value={userName}
          onChangeText={setUserName}
          style={{ width: '100%' }}
        />
        {isusername ? (
          <Text style={styles.error}>
            {strings.ForgotPassword.PLEASEENTEREMAIL}
          </Text>
        ) : (
          <></>
        )}
        <Text style={[{ marginTop: 25, fontSize: 15 }, styles.mainTextColor]}>
          {strings.ForgotPassword.DESCRIPTION}
        </Text>
      </View>
      <Button
        title={strings.ForgotPassword.SENDRESETLINK}
        onPress={SendResetLink}
      />
    </View>
  );
};

export default TutorLogin;
