import React, { useState } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, ScrollView,FlatList } from 'react-native';
import { IMAGE_CONSTANT, PrimaryColors } from '../../../Utils/Constants';
import { useNavigation } from '@react-navigation/native';
import LinearGradient from 'react-native-linear-gradient';
import { SafeAreaView, SafeAreaProvider } from 'react-native-safe-area-context';
import { useSelector } from 'react-redux';
import { RootState } from '../../../Redux/store';
import Ionicons from 'react-native-vector-icons/Ionicons';
import Header from '../../../CommonComponents/Header';
import strings from '../../../Utils/LocalizedStrings/LocalizedStrings';

const CreateTution = () => {
    const navigation = useNavigation();
    

   

    const isDarkMode = useSelector((state: RootState) => state.theme.isDarkMode);
    const styles = isDarkMode ? DarkStyles : LightStyles;
   
    return (
        <SafeAreaProvider style={styles.container}>
            {/* <LinearGradient
                colors={[PrimaryColors.BLACK, PrimaryColors.BLACK]}
                style={styles.lightHeader}
                start={{ x: 0, y: 0 }}
                end={{ x: 0, y: 1 }}>
                <Text style={{ fontSize: 25, fontWeight: 'bold', color: PrimaryColors.WHITE }}>CreateTution</Text>
            </LinearGradient> */}
            <Header title={strings.CreateTution.CREATETUTUION}/>
            <SafeAreaView style={{ flex: 1, width: '100%', paddingHorizontal: '20%' }}>
           
            </SafeAreaView>
        </SafeAreaProvider>
    );
};

export default CreateTution;

const LightStyles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: PrimaryColors.WHITE,
        alignItems: 'center',
    },
    lightHeader: {
        height: 70,
        width: '100%',
        borderBottomLeftRadius: 60,
        borderBottomRightRadius: 60,
        alignItems: 'center',
        justifyContent: 'center',
    },
    
});

const DarkStyles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: PrimaryColors.LIGHTGRAY,
        alignItems: 'center',
    },
    lightHeader: {
        height: 70,
        width: '100%',
        borderBottomLeftRadius: 60,
        borderBottomRightRadius: 60,
        alignItems: 'center',
        justifyContent: 'center',
    },
    
});
