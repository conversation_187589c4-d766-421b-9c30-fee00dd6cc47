import React, { useRef, useState, useEffect } from 'react';
import { 
  View, 
  Text, 
  StyleSheet, 
  TouchableOpacity, 
  TextInput, 
  useWindowDimensions, 
  Animated,
  Vibration
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import Toast from 'react-native-simple-toast';
import Ionicons from 'react-native-vector-icons/Ionicons';
import { useRoute, useNavigation } from '@react-navigation/native';
import { verifyOtp, resendOtp } from '../../../services/authService';
import AsyncStorage from '@react-native-async-storage/async-storage';

const OTPScreen = () => {
  const route = useRoute<any>();
  const navigation = useNavigation<any>();
  const { contactNo, firstName, lastName, flowType, email } = route.params || {};
  const [otp, setOtp] = useState(['', '', '', '', '', '']);
  const [error, setError] = useState('');
  const [activeIdx, setActiveIdx] = useState(0);
  const [isLoading, setIsLoading] = useState(false);
  const [timeLeft, setTimeLeft] = useState(60);
  const [canResend, setCanResend] = useState(false);
  
  const inputs = useRef<Array<TextInput | null>>([]);
  const { width } = useWindowDimensions();
  const isSmallScreen = width < 350;
  
  const shakeAnimation = useRef(new Animated.Value(0)).current;
  const fadeAnimation = useRef(new Animated.Value(0)).current;
  const scaleAnimation = useRef(new Animated.Value(0.9)).current;
  const progressAnimation = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    Animated.parallel([
      Animated.timing(fadeAnimation, {
        toValue: 1,
        duration: 800,
        useNativeDriver: true,
      }),
      Animated.spring(scaleAnimation, {
        toValue: 1,
        tension: 50,
        friction: 8,
        useNativeDriver: true,
      })
    ]).start();
  }, [fadeAnimation, scaleAnimation]);

  useEffect(() => {
    if (timeLeft > 0) {
      const timer = setTimeout(() => setTimeLeft(timeLeft - 1), 1000);
      return () => clearTimeout(timer);
    } else {
      setCanResend(true);
    }
  }, [timeLeft]);

  const shakeInputs = () => {
    Vibration.vibrate(100);
    Animated.sequence([
      Animated.timing(shakeAnimation, { toValue: 10, duration: 100, useNativeDriver: true }),
      Animated.timing(shakeAnimation, { toValue: -10, duration: 100, useNativeDriver: true }),
      Animated.timing(shakeAnimation, { toValue: 10, duration: 100, useNativeDriver: true }),
      Animated.timing(shakeAnimation, { toValue: 0, duration: 100, useNativeDriver: true }),
    ]).start();
  };

  const handleChange = (text: string, idx: number) => {
    if (/^\d*$/.test(text)) {
      const newOtp = [...otp];
      newOtp[idx] = text;
      setOtp(newOtp);
      setError('');
      if (text && idx < 5) {
        inputs.current[idx + 1]?.focus();
        setActiveIdx(idx + 1);
      }
      if (!text && idx > 0) {
        inputs.current[idx - 1]?.focus();
        setActiveIdx(idx - 1);
      }
      if (text && idx === 5) {
        setTimeout(() => handleConfirm(newOtp), 500);
      }
    }
  };

  const handleFocus = (idx: number) => {
    setActiveIdx(idx);
    setError('');
  };

  const handleConfirm = async (otpValue?: string[] | undefined) => {
    const otpToCheck = otpValue || otp;
    if (otpToCheck.join('').length !== 6) {
      setError('Please enter the complete 6-digit code');
      shakeInputs();
      return;
    }
    
    if (!contactNo) {
      setError('Contact number is required');
      shakeInputs();
      return;
    }
    
    setIsLoading(true);
    try {
      const verifyData: any = {
        contactNo,
        otp: otpToCheck.join(''),
        flowType: flowType || 'login',
      };
      
      if (firstName !== undefined && firstName !== null && firstName.trim() !== '') {
        verifyData.firstName = firstName.trim();
      }
      if (lastName !== undefined && lastName !== null && lastName.trim() !== '') {
        verifyData.lastName = lastName.trim();
      }
      if (email !== undefined && email !== null && email.trim() !== '') {
        verifyData.email = email.trim();
      }
      
      console.log('Sending verifyOtp with data:', verifyData);
      
      const result = await verifyOtp(verifyData);
      if (result && result.data && result.data.token) {
        await AsyncStorage.setItem('token', result.data.token);
      }
      // Store studentId in AsyncStorage if available
      if (result && result.data && (result.data.id || result.data.studentId)) {
        const sid = result.data.id?.toString() || result.data.studentId?.toString();
        await AsyncStorage.setItem('studentId', sid);
      }
      Toast.show('Verification successful!', Toast.SHORT);
      Animated.timing(progressAnimation, {
        toValue: 1,
        duration: 500,
        useNativeDriver: false,
      }).start();
      navigation.navigate('Home');
    } catch (error: any) {
      const msg = error?.response?.data?.message || '';
      console.log('verifyOtp error:', error?.response?.data);
      if (msg.toLowerCase().includes('otp') && msg.toLowerCase().includes('not match')) {
        setError('The OTP does not match. Please try again.');
        Toast.show('The OTP does not match. Please try again.', Toast.SHORT);
      } else {
        setError(msg || 'Invalid code. Please try again.');
        Toast.show(msg || 'Invalid code. Please try again.', Toast.SHORT);
      }
      shakeInputs();
    } finally {
      setIsLoading(false);
    }
  };

  const handleResend = async () => {
    if (!canResend) return;
    if (!contactNo) {
      setError('Contact number is required');
      return;
    }
    
    setTimeLeft(60);
    setCanResend(false);
    setError('');
    setOtp(['', '', '', '', '', '']);
    inputs.current[0]?.focus();
    setActiveIdx(0);
    try {
      const resendData: any = { 
        contactNo,
        flowType: flowType || 'login',
      };
      
      if (firstName !== undefined && firstName !== null && firstName.trim() !== '') {
        resendData.firstName = firstName.trim();
      }
      if (email !== undefined && email !== null && email.trim() !== '') {
        resendData.email = email.trim();
      }
      
      console.log('Sending resendOtp with data:', resendData);
      
      await resendOtp(resendData);
      Toast.show('OTP resent!', Toast.SHORT);
    } catch (error: any) {
      console.log('resendOtp error:', error?.response?.data);
      setError(error?.response?.data?.message || 'Failed to resend OTP');
      Toast.show(error?.response?.data?.message || 'Failed to resend OTP', Toast.SHORT);
    }
  };

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  return (
    <SafeAreaView style={styles.safeArea} edges={["top", "left", "right"]}>
      <View style={styles.gradientBackground}>
        <View style={[styles.bgShape1, { width: width * 0.8, height: width * 0.8 }]} />
        <View style={[styles.bgShape2, { width: width * 0.6, height: width * 0.6 }]} />
      </View>
      
      <Animated.View 
        style={[
          styles.container, 
          { 
            paddingHorizontal: isSmallScreen ? 20 : 32,
            opacity: fadeAnimation,
            transform: [{ scale: scaleAnimation }]
          }
        ]}
      >
        <View style={[styles.iconContainer, { width: isSmallScreen ? 80 : 96, height: isSmallScreen ? 80 : 96 }]}>
          <View style={[styles.iconCircle, { width: isSmallScreen ? 64 : 80, height: isSmallScreen ? 64 : 80 }]}>
            <Ionicons name="shield-checkmark-outline" size={isSmallScreen ? 32 : 40} color="#FD904B" />
          </View>
        </View>

        <Text style={[styles.heading, isSmallScreen && { fontSize: 24 }]}>
          Verify Your Identity
        </Text>
        <Text style={[styles.subheading, isSmallScreen && { fontSize: 14 }]}>
          We've sent a secure code to your device.{'\n'}Please enter it below to continue.
        </Text>

        <Animated.View 
          style={[
            styles.otpContainer,
            { transform: [{ translateX: shakeAnimation }] }
          ]}
        >
          <View style={[styles.otpRow, isSmallScreen && { gap: 8 }]}>
            {otp.map((digit, idx) => (
              <View key={idx} style={styles.inputWrapper}>
                <TextInput
                  ref={ref => { inputs.current[idx] = ref; }}
                  style={[
                    styles.otpInput,
                    {
                      width: isSmallScreen ? 42 : 52,
                      height: isSmallScreen ? 48 : 58,
                      fontSize: isSmallScreen ? 20 : 24,
                      borderColor: activeIdx === idx ? '#FD904B' : (error ? '#FF4444' : '#E8E8E8'),
                      backgroundColor: activeIdx === idx ? '#FFF8F4' : '#FAFAFA',
                      shadowColor: activeIdx === idx ? '#FD904B' : '#000',
                      shadowOpacity: activeIdx === idx ? 0.15 : 0.08,
                      borderWidth: activeIdx === idx ? 2 : 1.5,
                    },
                  ]}
                  keyboardType="number-pad"
                  maxLength={1}
                  value={digit}
                  onChangeText={text => handleChange(text, idx)}
                  onFocus={() => handleFocus(idx)}
                  returnKeyType={idx === 5 ? 'done' : 'next'}
                  autoFocus={idx === 0}
                  selectionColor="#FD904B"
                />
                {digit && (
                  <View style={styles.digitIndicator} />
                )}
              </View>
            ))}
          </View>
        </Animated.View>

        {error ? (
          <Animated.View style={styles.errorContainer}>
            <Ionicons name="alert-circle-outline" size={16} color="#FF4444" />
            <Text style={styles.errorText}>{error}</Text>
          </Animated.View>
        ) : null}

        <View style={styles.timerContainer}>
          {!canResend ? (
            <Text style={styles.timerText}>
              Resend code in {formatTime(timeLeft)}
            </Text>
          ) : (
            <TouchableOpacity style={styles.resendButton} onPress={handleResend}>
              <Ionicons name="refresh-outline" size={16} color="#FD904B" />
              <Text style={styles.resendText}>Resend Code</Text>
            </TouchableOpacity>
          )}
        </View>

        <TouchableOpacity
          style={[
            styles.confirmButton,
            { 
              width: isSmallScreen ? '100%' : '100%',
              opacity: isLoading ? 0.7 : 1
            }
          ]}
          onPress={() => handleConfirm()}
          activeOpacity={0.8}
          disabled={isLoading}
        >
          {isLoading ? (
            <View style={styles.loadingContainer}>
              <Animated.View style={[styles.loadingSpinner, { transform: [{ rotate: '360deg' }] }]} />
              <Text style={styles.buttonText}>Verifying...</Text>
            </View>
          ) : (
            <>
              <Text style={styles.buttonText}>Verify Code</Text>
              <Ionicons name="arrow-forward" size={18} color="#fff" style={styles.buttonIcon} />
            </>
          )}
        </TouchableOpacity>

        <View style={styles.progressContainer}>
          <Animated.View
            style={[
              styles.progressBar,
              {
                width: progressAnimation.interpolate({
                  inputRange: [0, 1],
                  outputRange: ['0%', '100%'],
                }),
              },
            ]}
          />
        </View>
      </Animated.View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
    backgroundColor: '#FAFBFC',
  },
  gradientBackground: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: 0,
  },
  bgShape1: {
    position: 'absolute',
    top: -200,
    left: -100,
    backgroundColor: '#FD904B08',
    borderRadius: 200,
    zIndex: 0,
  },
  bgShape2: {
    position: 'absolute',
    top: -150,
    right: -80,
    backgroundColor: '#FD904B05',
    borderRadius: 150,
    zIndex: 0,
  },
  container: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: 'transparent',
    zIndex: 1,
    paddingBottom: 80,
  },
  iconContainer: {
    marginBottom: 32,
    alignItems: 'center',
    justifyContent: 'center',
  },
  iconCircle: {
    backgroundColor: '#FFF8F4',
    borderRadius: 40,
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: '#FD904B',
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.12,
    shadowRadius: 16,
    elevation: 8,
    borderWidth: 1,
    borderColor: '#FD904B10',
  },
  heading: {
    fontSize: 28,
    fontWeight: '800',
    color: '#1A1A1A',
    marginBottom: 12,
    textAlign: 'center',
    letterSpacing: -0.5,
  },
  subheading: {
    fontSize: 16,
    color: '#6B7280',
    textAlign: 'center',
    marginBottom: 48,
    lineHeight: 22,
    fontWeight: '400',
  },
  otpContainer: {
    marginBottom: 24,
  },
  otpRow: {
    flexDirection: 'row',
    justifyContent: 'center',
    gap: 12,
  },
  inputWrapper: {
    position: 'relative',
  },
  otpInput: {
    borderRadius: 16,
    textAlign: 'center',
    fontWeight: '700',
    color: '#1A1A1A',
    shadowOffset: { width: 0, height: 4 },
    shadowRadius: 8,
    elevation: 3,
    fontSize: 24,
  },
  digitIndicator: {
    position: 'absolute',
    bottom: 8,
    left: '50%',
    width: 4,
    height: 4,
    backgroundColor: '#FD904B',
    borderRadius: 2,
    marginLeft: -2,
  },
  errorContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FFF5F5',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 12,
    marginBottom: 16,
    borderWidth: 1,
    borderColor: '#FFE5E5',
    gap: 8,
  },
  errorText: {
    color: '#FF4444',
    fontSize: 14,
    fontWeight: '500',
  },
  timerContainer: {
    marginBottom: 32,
    alignItems: 'center',
  },
  timerText: {
    color: '#9CA3AF',
    fontSize: 14,
    fontWeight: '500',
  },
  resendButton: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
    paddingHorizontal: 16,
    paddingVertical: 8,
  },
  resendText: {
    color: '#FD904B',
    fontSize: 15,
    fontWeight: '600',
  },
  confirmButton: {
    backgroundColor: '#FD904B',
    paddingVertical: 18,
    paddingHorizontal: 24,
    borderRadius: 16,
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: '#FD904B',
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.25,
    shadowRadius: 16,
    elevation: 8,
    flexDirection: 'row',
    gap: 8,
  },
  buttonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '700',
    letterSpacing: 0.5,
  },
  buttonIcon: {
    marginLeft: 4,
  },
  loadingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  loadingSpinner: {
    width: 20,
    height: 20,
    borderWidth: 2,
    borderColor: '#FFFFFF40',
    borderTopColor: '#FFFFFF',
    borderRadius: 10,
  },
  progressContainer: {
    width: '100%',
    height: 3,
    backgroundColor: '#E5E7EB',
    borderRadius: 2,
    marginTop: 24,
    overflow: 'hidden',
  },
  progressBar: {
    height: '100%',
    backgroundColor: '#FD904B',
    borderRadius: 2,
  },
});

export default OTPScreen;