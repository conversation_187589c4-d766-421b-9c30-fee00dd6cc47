import React, { useState } from 'react';
import { View, StyleSheet, TouchableOpacity, Text, KeyboardAvoidingView, Platform, Image, ScrollView } from 'react-native';
import CommonAnimatedTextInput from '../../../CommonComponents/CommonAnimatedTextInput';
import { IMAGE_CONSTANT, PrimaryColors } from '../../../Utils/Constants';
import { useNavigation } from '@react-navigation/native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { registerStudent } from '../../../services/authService';
import Toast from 'react-native-simple-toast';

const validateName = (name: string) => name.length > 1;
const validateMobile = (mobile: string) => /^\d{10,15}$/.test(mobile);

const SignUpScreen = () => {
  const [firstName, setFirstName] = useState('');
  const [lastName, setLastName] = useState('');
  const [mobile, setMobile] = useState('');
  const [iconColors, setIconColors] = useState({
    firstName: '#FD904B',
    lastName: '#FD904B',
    mobile: '#FD904B',
  });
  const navigation = useNavigation<any>();

  const handleSignUp = async () => {
    if (!validateName(firstName) || !validateName(lastName) || !validateMobile(mobile)) {
      Toast.show('Please fill all fields correctly', Toast.SHORT);
      return;
    }
    try {
      await registerStudent({
        firstName,
        lastName,
        contactNo: mobile,
        flowType: 'registration',
      });
      Toast.show('OTP sent to your number', Toast.SHORT);
      navigation.navigate('OTP', {
        contactNo: mobile,
        firstName,
        lastName,
        flowType: 'registration',
      });
    } catch (error: any) {
      const msg = error?.response?.data?.message || '';
      if (msg.toLowerCase().includes('already registered')) {
        Toast.show('This mobile number is already registered.', Toast.SHORT);
      } else {
        Toast.show(msg || 'Registration failed', Toast.SHORT);
      }
    }
  };

  return (
    <SafeAreaView style={styles.safeArea} edges={["top", "left", "right"]}>
      <KeyboardAvoidingView
        style={styles.container}
        behavior={Platform.OS === 'ios' ? 'padding' : undefined}
        keyboardVerticalOffset={Platform.OS === 'ios' ? 0 : 0}
      >
        <ScrollView
          contentContainerStyle={styles.scrollContent}
          keyboardShouldPersistTaps="handled"
          showsVerticalScrollIndicator={false}
        >
          <View style={styles.gradientBg} />
          <Image source={IMAGE_CONSTANT.STUDENT} style={styles.illustration} />
          <Text style={styles.headerTitle}>Create Account</Text>
          <Text style={styles.subheading}>Join our learning community!</Text>

          <View style={styles.form}>
            <CommonAnimatedTextInput
              label="First Name"
              value={firstName}
              onChangeText={setFirstName}
              autoCapitalize="words"
              icon="person-outline"
              iconColor={iconColors.firstName}
              onPressIn={() => setIconColors((c) => ({ ...c, firstName: '#FD904B' }))}
              onPressOut={() => setIconColors((c) => ({ ...c, firstName: validateName(firstName) ? '#4BB543' : '#FD904B' }))}
            />
            <CommonAnimatedTextInput
              label="Last Name"
              value={lastName}
              onChangeText={setLastName}
              autoCapitalize="words"
              icon="person-outline"
              iconColor={iconColors.lastName}
              onPressIn={() => setIconColors((c) => ({ ...c, lastName: '#FD904B' }))}
              onPressOut={() => setIconColors((c) => ({ ...c, lastName: validateName(lastName) ? '#4BB543' : '#FD904B' }))}
            />
            <CommonAnimatedTextInput
              label="Mobile Number"
              value={mobile}
              onChangeText={setMobile}
              keyboardType="phone-pad"
              maxLength={15}
              icon="call-outline"
              iconColor={iconColors.mobile}
              onPressIn={() => setIconColors((c) => ({ ...c, mobile: '#FD904B' }))}
              onPressOut={() => setIconColors((c) => ({ ...c, mobile: validateMobile(mobile) ? '#4BB543' : '#FD904B' }))}
            />
            <TouchableOpacity style={styles.button} onPress={handleSignUp} activeOpacity={0.85}>
              <Text style={styles.buttonText}>Sign Up</Text>
            </TouchableOpacity>
            <Text style={styles.privacyText}>
              By signing up, you agree to our
              <Text style={styles.linkText}> Terms</Text> &
              <Text style={styles.linkText}> Privacy Policy</Text>.
            </Text>
          </View>

          <TouchableOpacity style={styles.signInRow} onPress={() => navigation.navigate('Login')}>
            <Text style={styles.signInText}>Already have an account? <Text style={styles.signInLink}>Sign In</Text></Text>
          </TouchableOpacity>
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
    backgroundColor: '#fff',
  },
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  scrollContent: {
    flexGrow: 1,
    justifyContent: 'flex-start',
    paddingBottom: 24,
    alignItems: 'center',
  },
  gradientBg: {
    position: 'absolute',
    top: -120,
    left: -80,
    width: 350,
    height: 350,
    borderRadius: 175,
    backgroundColor: '#FD904B22',
    zIndex: 0,
  },
  illustration: {
    width: 110,
    height: 110,
    alignSelf: 'center',
    marginTop: 24,
    marginBottom: 8,
    resizeMode: 'contain',
    zIndex: 1,
  },
  headerTitle: {
    fontSize: 26,
    fontWeight: '700',
    color: PrimaryColors.BLACK,
    letterSpacing: 0.2,
    textAlign: 'center',
    marginTop: 8,
    zIndex: 1,
  },
  subheading: {
    fontSize: 15,
    color: '#656565',
    textAlign: 'center',
    marginBottom: 18,
    marginTop: 2,
    zIndex: 1,
  },
  form: {
    width: '100%',
    alignItems: 'center',
    paddingVertical: 12,
    backgroundColor: '#fff',
    borderTopLeftRadius: 24,
    borderTopRightRadius: 24,
    zIndex: 1,
  },
  button: {
    marginTop: 32,
    width: '85%',
    backgroundColor: '#FD904B',
    paddingVertical: 16,
    borderRadius: 12,
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: '#FD904B',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.18,
    shadowRadius: 8,
    elevation: 5,
  },
  buttonText: {
    color: '#fff',
    fontSize: 18,
    fontWeight: '700',
    letterSpacing: 0.5,
  },
  privacyText: {
    marginTop: 18,
    fontSize: 12,
    color: '#888',
    textAlign: 'center',
    width: '85%',
  },
  linkText: {
    color: '#FD904B',
    fontWeight: '600',
  },
  signInRow: {
    marginTop: 32,
    alignSelf: 'center',
    zIndex: 1,
  },
  signInText: {
    color: '#888',
    fontSize: 15,
    textAlign: 'center',
  },
  signInLink: {
    color: '#FD904B',
    fontWeight: '700',
  },
});

export default SignUpScreen; 