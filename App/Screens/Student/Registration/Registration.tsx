/* eslint-disable react-native/no-inline-styles */
import React, {useState} from 'react';
import {View, Text, StyleSheet, TouchableOpacity} from 'react-native';
import {PrimaryColors} from '../../../Utils/Constants';
import {useNavigation} from '@react-navigation/native';
import strings from '../../../Utils/LocalizedStrings/LocalizedStrings';
import HeaderWithLogo from '../../../CommonComponents/HeaderWithLogo';
import IndexStyle from '../../../Theme/IndexStyle';
import Button from '../../../CommonComponents/Button';
import {studentRegister} from '../../../services/studentService';
import Toast from 'react-native-simple-toast';
import CommonTextInput from '../../../CommonComponents/CommonTextInput';

const Registration = () => {
  const navigation = useNavigation<any>();
  const {styles} = IndexStyle();

  const [firstName, setFirstName] = useState('');
  const [lastName, setLastName] = useState('');
  const [email, setEmail] = useState('');
  const [contact, setContact] = useState('');
  const [password, setPassword] = useState('');

  const [errors, setErrors] = useState({
    firstName: false,
    lastName: false,
    email: false,
    contact: false,
    password: false,
  });

  const handleRegister = async () => {
    if (password.length < 6) {
      Toast.show(strings.Registration.PASSWORDLENGTH, Toast.SHORT);
      return;
    }
    const trimmed = {
      firstName: firstName.trim(),
      lastName: lastName.trim(),
      email: email.trim(),
      contact: email.trim(),
      password: password.trim(),
    };

    const newErrors = {
      firstName: trimmed.firstName === '',
      lastName: trimmed.lastName === '',
      email: trimmed.email === '',
      password: trimmed.password === '',
      contact: trimmed.contact === '',
    };

    setErrors(newErrors);

    if (Object.values(newErrors).some(Boolean)) {
      return;
    }
    let registrationData = {
      firstName: firstName,
      lastName: lastName,
      email: email,
      contact: contact,
      password: password,
    };
    try {
      const data = await studentRegister(registrationData);
      if (data && data.status === 200) {
        navigation.navigate('Login');
      } else if (data && data.status === 400) {
        Toast.show(strings.Registration.STUDENTALREADYEXIST, Toast.SHORT);
      } else if (data && data.status === 500) {
        Toast.show(strings.Registration.INTERNALSERVERERROR, Toast.SHORT);
      } else if (data && data.status === 403) {
        Toast.show(strings.Registration.RECAPCHAVALIDATIONFAILED, Toast.SHORT);
      }
    } catch (error) {
      console.log('ERROR IN STUDENT REGISTER:::', error);
    }
  };

  return (
    <View style={styles.container}>
      <HeaderWithLogo />

      <View style={[BaseStyle.inputContainer, {marginBottom: '2%'}]}>
        <CommonTextInput
          label={strings.Registration.FIRSTNAME}
          placeholder={strings.Registration.ENTERFIRSTNAME}
          value={firstName}
          onChangeText={setFirstName}
          style={{width: '100%'}}
        />
        {errors.firstName && (
          <Text style={styles.error}>
            {strings.Registration.PLEASEENTERFIRSTNAME}
          </Text>
        )}
      </View>

      <View style={[BaseStyle.inputContainer, {marginBottom: '2%'}]}>
        <CommonTextInput
          label={strings.Registration.LASTNAME}
          placeholder={strings.Registration.ENTERLASTNAME}
          value={lastName}
          onChangeText={setLastName}
          style={{width: '100%'}}
        />
        {errors.lastName && (
          <Text style={styles.error}>
            {strings.Registration.PLEASEENTERLASTNAME}
          </Text>
        )}
      </View>

      <View style={[BaseStyle.inputContainer, {marginBottom: '2%'}]}>
        <CommonTextInput
          label={strings.Registration.EMAIL}
          placeholder={strings.Registration.ENTEREMAIL}
          value={email}
          onChangeText={setEmail}
          keyboardType="email-address"
          style={{width: '100%'}}
        />
        {errors.email && (
          <Text style={styles.error}>
            {strings.Registration.PLEASEENTEREMAIL}
          </Text>
        )}
      </View>

      <View style={[BaseStyle.inputContainer, {marginBottom: '2%'}]}>
        <CommonTextInput
          label={strings.Registration.CONTACAT}
          placeholder={strings.Registration.ENTERCONTACT}
          value={contact}
          onChangeText={setContact}
          style={{width: '100%'}}
        />
        {errors.contact && (
          <Text style={styles.error}>
            {strings.Registration.PLEASEENTERCONTACT}
          </Text>
        )}
      </View>

      <View style={[BaseStyle.inputContainer, {marginBottom: '2%'}]}>
        <CommonTextInput
          label={strings.Registration.PASSWORD}
          placeholder={strings.Registration.ENTERPASSWORD}
          value={password}
          onChangeText={setPassword}
          style={{width: '100%'}}
        />
        {password.length > 0 && password.length < 6 && (
          <Text style={styles.error}>
            {strings.Registration.PASSWORDLENGTH}
          </Text>
        )}
        {errors.password && (
          <Text
            style={{color: PrimaryColors.ERROR, fontSize: 14, marginTop: 4}}>
            {strings.Registration.PLEASEENTERPASSWORD}
          </Text>
        )}
      </View>

      <Button title={strings.Registration.REGISTER} onPress={handleRegister} />

      <TouchableOpacity
        onPress={() => navigation.navigate('Login')}
        style={{marginTop: 16}}>
        <Text
          style={{
            color: PrimaryColors.ORANGE,
            fontSize: 16,
            fontWeight: '500',
          }}>
          {strings.Registration.ALREADYHAVEACCOUNT}
        </Text>
      </TouchableOpacity>
    </View>
  );
};

export default Registration;

const BaseStyle = StyleSheet.create({
  inputContainer: {
    width: '80%',
    marginBottom: 20,
  },
});
