/* eslint-disable react-native/no-inline-styles */
import React, {useEffect, useCallback, useState} from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Platform,
  Image,
  Linking,
} from 'react-native';
import {useFocusEffect, useNavigation} from '@react-navigation/native';
import {SafeAreaView, SafeAreaProvider} from 'react-native-safe-area-context';
import Ionicons from 'react-native-vector-icons/Ionicons';
import {PrimaryColors} from '../../../Utils/Constants';
import IndexStyle from '../../../Theme/IndexStyle';
import strings from '../../../Utils/LocalizedStrings/LocalizedStrings';
import CommonTextInput from '../../../CommonComponents/CommonTextInput';
import CommonDateTimePicker from '../../../CommonComponents/CommonDateTimePicker';
import {PermissionsAndroid} from 'react-native';
import CommonDropdownPicker from '../../../CommonComponents/CommonDropdownPicker';
import {imgBaseUrl} from '../../../config/apiUrl';
import Toast from 'react-native-simple-toast';
import {launchCamera} from 'react-native-image-picker';
import {pick} from '@react-native-documents/picker';
import RNBlobUtil from 'react-native-blob-util';
import NavigationHeader from '../../../CommonComponents/NavigationHeader';
import {request, PERMISSIONS, RESULTS} from 'react-native-permissions';
import {ActivityIndicator} from 'react-native';
import { getStudentProfileData, updateStudentProfileData } from '../../../services/studentProfileService';
import { getCurrentStudent } from '../../../services/studentService';

const StudentProfile = () => {
  const navigation = useNavigation<any>();
  const {styles, isDarkMode} = IndexStyle();
  const [firstName, setFirstName] = useState('');
  const [lastName, setLastName] = useState('');
  const [email, setEmail] = useState('');
  const [contact, setContact] = useState('');
  const [medium, setMedium] = useState('');
  const [classroom, setClassroom] = useState('');
  const [birthday, setBirthday] = useState<any>(null);
  const [school, setSchoolName] = useState('');
  const [address, setAddress] = useState('');
  const [photo, setPhoto] = useState<any>(null);
  const [document, setDocument] = useState('');
  const [classroomOptions, setClassroomOptions] = useState([]); //right now dont use this as we static set standard dropdown
  const [photoMimeType, setPhotoMimeType] = useState('');
  const [documentName, setDocumentName] = useState('');
  const [documentMimeType, setDocumentMimeType] = useState('');
  const [isLoading, setIsLoading] = useState(false);

  const [errors, setErrors] = useState({
    firstName: '',
    lastName: '',
    email: '',
    contact: '',
    medium: '',
    classroom: '',
    birthday: '',
    school: '',
    address: '',
    photo: '',
    document: '',
  });

  const mediumOptions = [
    {label: 'Gujarati', value: 'gujarati'},
    {label: 'English', value: 'english'},
  ];

  const staticClassroomOptions = [
    {label: 'STD 1', value: 'STD 1'},
    {label: 'STD 2', value: 'STD 2'},
    {label: 'STD 3', value: 'STD 3'},
    {label: 'STD 4', value: 'STD 4'},
    {label: 'STD 5', value: 'STD 5'},
    {label: 'STD 6', value: 'STD 6'},
    {label: 'STD 7', value: 'STD 7'},
    {label: 'STD 8', value: 'STD 8'},
    {label: 'STD 9', value: 'STD 9'},
    {label: 'STD 10', value: 'STD 10'},
    {label: 'STD 11', value: 'STD 11'},
    {label: 'STD 12', value: 'STD 12'},
  ];

  const getImageSource = (image: string, mimeType = 'image/jpeg') => {
    if (!image) {return null;}
    const isBase64 =
      typeof image === 'string' &&
      (image.startsWith('/9j/') ||
        image.startsWith('iVBORw0KGgo') ||
        image.includes('data:image') ||
        image.length > 100);

    if (isBase64) {
      console.log('Rendering base64 image:', {mimeType, length: image.length});
      if (!image.startsWith('data:image')) {
        return {uri: `data:${mimeType};base64,${image}`};
      }
      return {uri: image};
    }

    let url = image;
    if (
      image.startsWith(imgBaseUrl) &&
      (image.includes('/9j/') || image.includes('iVBORw0KGgo'))
    ) {
      const base64Part = image.replace(imgBaseUrl, '').replace(/^\/+/, '');
      console.log('Extracted base64 from URL:', {base64Part, mimeType});
      return {uri: `data:${mimeType};base64,${base64Part}`};
    }
    if (!image.startsWith('http')) {
      url = `${imgBaseUrl}/${image}`.replace(/\/+/g, '/');
    }
    console.log('Rendering remote image:', url);
    return {uri: url};
  };
  useEffect(() => {
    fetchStudentData();
  }, []);
  useFocusEffect(
    useCallback(() => {
      fetchStudentData();
      return () => {};
    }, []),
  );
  const fetchStudentData = async () => {
    try {
      // Fetch basic student info
      const studentRes = await getCurrentStudent();
      setFirstName(studentRes.data.firstName || '');
      setLastName(studentRes.data.lastName || '');
      setContact(studentRes.data.contact || '');
      setEmail(studentRes.data.email || '');
      // Fetch profile info
      const data = await getStudentProfileData();
      const profile = data.data.profile || {};
      const classroomOptionsFromApi = data.data.classroomOptions || [];
      setMedium(profile.medium || '');
      setClassroom(profile.classroom || '');
      setBirthday(profile.birthday ? new Date(profile.birthday) : null);
      setSchoolName(profile.school || '');
      setAddress(profile.address || '');
      setPhoto(profile.photo || '');
      setDocument(profile.documentUrl || '');
      setPhotoMimeType(profile.photoMimeType || 'image/jpeg');
      setDocumentMimeType(profile.documentMimeType || 'image/jpeg');
      const formattedClassroomOptions = classroomOptionsFromApi.map(
        (option: any) => ({
          label: option.value,
          value: option.value,
        }),
      );
      setClassroomOptions(formattedClassroomOptions);
      console.log('PARSED DATA:', data);
    } catch (err) {
      console.log('ERROR IN GET STUDENT DATA:', err);
    }
  };

  const handleStudentUpdateProfile = async () => {
    setIsLoading(true);
    const newErrors: any = {};

    if (!firstName.trim())
      {newErrors.firstName = strings.StudentProfile.PLEASEENTERFIRSTNAME;}
    if (!lastName.trim())
      {newErrors.lastName = strings.StudentProfile.PLEASEENTERLASTNAME;}
    if (!email.trim())
      {newErrors.email = strings.StudentProfile.PLEASEENTEREMAIL;}
    if (!contact.trim())
      {newErrors.contact = strings.StudentProfile.PLEASEENTERCONTACT;}
    if (!medium.trim()) {newErrors.medium = strings.StudentProfile.MEDIUM;}
    if (!classroom.trim())
      {newErrors.classroom = strings.StudentProfile.CLASSROM;}
    if (!birthday)
      {newErrors.birthday = strings.StudentProfile.PLEASESELECTDATEOFBIRTH;}
    if (!school.trim())
      {newErrors.school = strings.StudentProfile.PLEASEENTERSCHOOLNAME;}
    if (!photo.trim())
      {newErrors.photo = strings.StudentProfile.PLEASEENTERPROFILEPHOTO;}
    if (!document.trim())
      {newErrors.document = strings.StudentProfile.PLEASEENTERDOCUMENT;}

    setErrors(newErrors);

    if (Object.keys(newErrors).length === 0) {
      console.log('Form is valid');
    }

    try {
      const payload = {
        firstName,
        lastName,
        email,
        contact,
        medium,
        classroom,
        birthday,
        school,
        address,
        photo,
        photoMimeType: photoMimeType,
        document,
        documentMimeType: documentMimeType,
        documentName: documentName,
      };
      const data = await updateStudentProfileData(payload);
      setIsLoading(false);
      Toast.show('Student Profile Updated Successfully', Toast.SHORT);
    } catch (error) {
      setIsLoading(false);
      console.log('Error while updating Student profile:', error);
    }
  };

  const formatDate = (dateString: string) => {
    if (!dateString) {return '';}
    const date = new Date(dateString);
    const day = String(date.getDate()).padStart(2, '0');
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const year = date.getFullYear();
    return `${day}/${month}/${year}`;
  };

  const requestCameraPermission = async () => {
    if (Platform.OS === 'android') {
      try {
        const granted = await PermissionsAndroid.request(
          PermissionsAndroid.PERMISSIONS.CAMERA,
        );

        if (granted === PermissionsAndroid.RESULTS.GRANTED) {
          openCamera();
        } else {
          Toast.show('Camera permission denied', Toast.SHORT);
        }
      } catch (err) {
        console.warn('Camera permission error:', err);
      }
    } else {
      // openCamera();
      console.log('IN IOS CAMERA PERMISSION');
      try {
        const result = await request(PERMISSIONS.IOS.CAMERA);

        if (result === RESULTS.GRANTED) {
          console.log('CAMERA PERMISSION GRANTED::');
          openCamera();
        } else {
          Toast.show('Camera permission denied', Toast.SHORT);
        }
      } catch (err) {
        console.warn('iOS camera permission error:', err);
      }
    }
  };

  const openCamera = () => {
    const options = {
      mediaType: 'photo',
      cameraType: 'front',
      saveToPhotos: true,
      includeBase64: true,
    };

    launchCamera(options, response => {
      if (response.didCancel) {
        console.log('User cancelled camera');
      } else if (response.errorCode) {
        console.log('Camera error:', response.errorMessage);
      }
      const asset = response.assets?.[0];
      if (asset) {
        const base64 = asset.base64;
        const mimeType = asset.type || 'image/jpeg';
        setPhoto(base64);
        setPhotoMimeType(mimeType);
      }
    });
  };

  const requestFileUploadPermission = async () => {
    if (Platform.OS === 'android') {
      try {
        const permission =
          Platform.Version >= 33
            ? PermissionsAndroid.PERMISSIONS.READ_MEDIA_IMAGES
            : PermissionsAndroid.PERMISSIONS.READ_EXTERNAL_STORAGE;

        const granted = await PermissionsAndroid.request(permission);

        if (granted === PermissionsAndroid.RESULTS.GRANTED) {
          openFileManager();
        } else {
          Toast.show('File access permission denied', Toast.SHORT);
        }
      } catch (err) {
        console.warn('File upload permission error:', err);
      }
    } else {
      // openFileManager();
      try {
        const result = await request(PERMISSIONS.IOS.PHOTO_LIBRARY);

        if (result === RESULTS.GRANTED) {
          openFileManager();
        } else {
          Toast.show('Photo library access denied', Toast.SHORT);
        }
      } catch (err) {
        console.warn('iOS file upload permission error:', err);
      }
    }
  };

  const convertContentUriToBase64 = async (contentUri: any) => {
    try {
      const base64 = await RNBlobUtil.fs.readFile(contentUri, 'base64');
      return base64;
    } catch (error) {
      console.error('Failed to convert file to base64:', error);
      return null;
    }
  };

  const openFileManager = async () => {
    try {
      const files = await pick();
      const file = Array.isArray(files) ? files[0] : files;

      if (!file?.uri) {
        Toast.show('No file selected', Toast.SHORT);
        return;
      }
      const base64String = await convertContentUriToBase64(file.uri);
      setDocument(base64String);
      setDocumentMimeType(file.type || 'application/octet-stream');
      setDocumentName(file.name || 'document');
    } catch (error) {
      console.warn('Document picker error:', error);
    }
  };

  const openDocument = async (url: any) => {
    try {
      await Linking.openURL(url);
    } catch (err) {
      console.warn('Failed to open document:', err);
    }
  };
  console.log('profile Photot from the web', photo);
  return (
    <SafeAreaProvider
      style={[
        {
          backgroundColor: isDarkMode
            ? PrimaryColors.BLACK
            : PrimaryColors.WHITE,
        },
      ]}>
      <NavigationHeader isBack={true} title="Personal Info" onBackPress={()=>{
          navigation.goBack();
        }} />
      <SafeAreaView
        style={{
          flex: 1,
          width: '100%',
          paddingHorizontal: '20%',
          paddingBottom: '30%',
        }}
        edges={['left', 'right']}>
        <ScrollView showsVerticalScrollIndicator={false} style={{}}>
          <Text
            style={[
              styles.label1,
              {
                color: isDarkMode ? PrimaryColors.WHITE : PrimaryColors.BLACK,
                fontSize: 18,
              },
            ]}>
            Personal Details
          </Text>
          <Text style={styles.label1}>
            {strings.StudentProfile.PROFILEPHOTO}
          </Text>
          <Text style={styles.label1}>
            {strings.StudentProfile.PHOTODESCRIPTION}
          </Text>
          <>{console.log('IMAGE URI:::', imgBaseUrl + '/' + photo)}</>
          {photo ? (
            <Image
              // source={{
              //   uri: photo.startsWith('http')
              //     ? Platform.OS === 'ios' ? encodeURI(photo) : photo
              //     : `${imgBaseUrl}/${photo}`,
              // }}
              source={getImageSource(photo, photoMimeType)}
              style={{
                width: 100,
                height: 100,
                borderRadius: 50,
                alignSelf: 'center',
              }}
            />
          ) : null}

          <TouchableOpacity
            style={formStyles.button}
            onPress={requestCameraPermission}>
            <Ionicons
              name="camera-outline"
              size={30}
              color={PrimaryColors.BLACK}
            />
            <Text
              style={{
                color: PrimaryColors.BLACK,
                fontSize: 18,
                fontWeight: 'bold',
              }}>
              Upload Your Photo
            </Text>
          </TouchableOpacity>

          <CommonTextInput
            label={strings.StudentProfile.FIRSTNAME}
            placeholder={strings.StudentProfile.ENTERYOURFIRSTNAME}
            value={firstName}
            onChangeText={setFirstName}
            style={{width: '100%'}}
          />
          {errors.firstName ? (
            <Text style={[formStyles.errorText, {marginTop: '2%'}]}>
              {errors.firstName}
            </Text>
          ) : null}

          <CommonTextInput
            label={strings.StudentProfile.LASTNAME}
            placeholder={strings.StudentProfile.ENTERYOURLASTNAME}
            value={lastName}
            onChangeText={setLastName}
            style={{width: '100%'}}
          />
          {errors.lastName ? (
            <Text style={[formStyles.errorText, {marginTop: '2%'}]}>
              {errors.lastName}
            </Text>
          ) : null}

          <CommonTextInput
            label={strings.StudentProfile.EMAIL}
            placeholder={strings.StudentProfile.ENTERYOUREMAIL}
            value={email}
            onChangeText={setEmail}
            style={{width: '100%'}}
          />
          {errors.email ? (
            <Text style={[formStyles.errorText, {marginTop: '2%'}]}>
              {errors.email}
            </Text>
          ) : null}

          <CommonTextInput
            label={strings.StudentProfile.CONTACT}
            placeholder={strings.StudentProfile.ENTERYOURCONTACT}
            value={contact}
            onChangeText={setContact}
            style={{width: '100%'}}
          />
          {errors.contact ? (
            <Text style={[formStyles.errorText, {marginTop: '2%'}]}>
              {errors.contact}
            </Text>
          ) : null}

          <CommonDateTimePicker
            style={{width: '100%', marginBottom: '2%', marginTop: '2%'}}
            label={strings.StudentProfile.DATEOFBIRTH}
            value={birthday}
            innerText={
              formatDate(birthday) || strings.StudentProfile.SELECTYOURBIRTHDAY
            }
            onChange={val => {
              setBirthday(val);
              setErrors({...errors, birthday: ''});
            }}
            mode="date"
          />
          {errors.birthday ? (
            <Text style={formStyles.errorText}>{errors.birthday}</Text>
          ) : null}
          <Text style={[formStyles.helperText, {marginBottom: '4%'}]}>
            {strings.StudentProfile.DOBDESCRIPTION}
          </Text>
          <Text
            style={[
              styles.label1,
              {
                color: isDarkMode ? PrimaryColors.WHITE : PrimaryColors.BLACK,
                fontSize: 18,
              },
            ]}>
            Other Details
          </Text>
          <CommonDropdownPicker
            label={strings.StudentProfile.MEDIUM}
            data={mediumOptions}
            value={medium}
            onChange={setMedium}
            placeholder={strings.StudentProfile.SELECTMEDIUM}
            numberOfLines={1}
            ellipsizeMode="tail"
            style={{color: isDarkMode ? '#6E6E6E' : '#8D8D8D'}}
          />
          {errors.medium ? (
            <Text style={[formStyles.errorText, {marginTop: '2%'}]}>
              {errors.medium}
            </Text>
          ) : null}

          <CommonDropdownPicker
            label={strings.StudentProfile.CLASSROM}
            // data={classroomOptions}
            data={staticClassroomOptions}
            value={classroom}
            onChange={setClassroom}
            placeholder={strings.StudentProfile.SELECTCLASSROOM}
            numberOfLines={1}
            ellipsizeMode="tail"
            style={{marginTop: '2%'}}
          />
          {errors.classroom ? (
            <Text style={[formStyles.errorText, {marginTop: '2%'}]}>
              {errors.classroom}
            </Text>
          ) : null}

          <View style={{marginVertical: 16}}>
            <Text style={styles.label1}>
              {strings.StudentProfile.IDENTITYDOCUMENT}
            </Text>
            <Text
              style={{
                textAlign: 'justify',
                color: isDarkMode ? PrimaryColors.WHITE : PrimaryColors.BLACK,
              }}>
              {strings.StudentProfile.IDENTITYDOCUMENTDESCRIPTION}
            </Text>

            {document ? (
              <TouchableOpacity
                // onPress={() => openDocument(`${imgBaseUrl}/${document}`)}>
                onPress={() =>
                  openDocument(
                    getImageSource(document, documentMimeType)?.uri ||
                      `${imgBaseUrl}/${document}`,
                  )
                }>
                <Image
                  // source={{uri: `${imgBaseUrl}/${document}`}}
                  source={getImageSource(document, documentMimeType)}
                  style={{
                    width: 200,
                    height: 100,
                    marginTop: 10,
                    alignSelf: 'center',
                  }}
                  resizeMode="contain"
                />
                <Text
                  style={[
                    formStyles.helperText,
                    {textAlign: 'center', color: PrimaryColors.ORANGE},
                  ]}>
                  {strings.StudentProfile.TAPTOVIEWDOCUMENT}
                </Text>
              </TouchableOpacity>
            ) : (
              <Text style={formStyles.helperText}>
                {strings.StudentProfile.NODOCUMENTUPLOADED}
              </Text>
            )}

            <TouchableOpacity
              style={[
                formStyles.uploadBox,
                {backgroundColor: isDarkMode ? '#1B1B1B' : '#FFFFFF'},
              ]}
              onPress={requestFileUploadPermission}>
              <Ionicons
                name="cloud-upload-outline"
                size={24}
                color={PrimaryColors.ORANGE}
              />
              <Text
                style={{
                  fontWeight: 'bold',
                  color: isDarkMode ? PrimaryColors.WHITE : PrimaryColors.BLACK,
                }}>
                {strings.StudentProfile.CLICKTOUPLOAD}
              </Text>
              <Text style={formStyles.helperText}>
                {strings.StudentProfile.FILETYPEDESCRIPTION}
              </Text>
            </TouchableOpacity>
          </View>

          <View style={{marginVertical: 16}}>
            <Text
              style={[
                styles.label1,
                {
                  color: isDarkMode ? PrimaryColors.WHITE : PrimaryColors.BLACK,
                  fontSize: 18,
                },
              ]}>
              {strings.StudentProfile.SCHOOLINFORMATION}s
            </Text>
            <CommonTextInput
              label={strings.StudentProfile.ENTERYOURSCHOOLNAME}
              placeholder={strings.StudentProfile.ENTERYOURSCHOOLNAME}
              value={school}
              onChangeText={(val: string) => {
                setSchoolName(val);
                setErrors({...errors, school: ''});
              }}
              style={{width: '100%'}}
            />
            {errors.school ? (
              <Text style={formStyles.errorText}>{errors.school}</Text>
            ) : null}

            <CommonTextInput
              label={strings.StudentProfile.ENTERYOURFULLADDRESS}
              placeholder={strings.StudentProfile.ENTERYOURFULLADDRESS}
              value={address}
              onChangeText={(val: string) => {
                setAddress(val);
                setErrors({...errors, address: ''});
              }}
              style={{width: '100%'}}
              multiline
            />
            {errors.address ? (
              <Text style={formStyles.errorText}>{errors.address}</Text>
            ) : null}
          </View>
          <TouchableOpacity
            style={[formStyles.button, {marginBottom: 20}]}
            onPress={handleStudentUpdateProfile}>
            {isLoading ? (
              <ActivityIndicator
                size="small"
                color={isDarkMode ? PrimaryColors.WHITE : PrimaryColors.BLACK}
              />
            ) : null}
            <Text style={[formStyles.buttonText, {color: PrimaryColors.BLACK}]}>
              {strings.StudentProfile.SUBMIT}
            </Text>
          </TouchableOpacity>
        </ScrollView>
      </SafeAreaView>
    </SafeAreaProvider>
  );
};

export default StudentProfile;

const formStyles = StyleSheet.create({
  uploadBox: {
    borderWidth: 2,
    borderColor: PrimaryColors.GRAYSHADOW,
    borderStyle: 'dashed',
    borderRadius: 8,
    padding: 16,
    alignItems: 'center',
    marginTop: 10,
  },
  button: {
    flexDirection: 'row',
    backgroundColor: '#FF914d',
    padding: 10,
    borderRadius: 8,
    marginTop: 10,
    alignItems: 'center',
    justifyContent: 'center',
    gap: 8,
    marginBottom: '4%',
  },
  buttonText: {
    color: PrimaryColors.WHITE,
    fontWeight: 'bold',
    fontSize: 19,
  },
  helperText: {
    fontSize: 12,
    color: PrimaryColors.GRAYSHADOW,
    marginTop: 4,
  },
  errorText: {
    fontSize: 12,
    color: PrimaryColors.ERROR,
    marginBottom: '3%',
  },
});
