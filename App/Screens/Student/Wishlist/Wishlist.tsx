import React, {useEffect, useState} from 'react';
import {
  View,
  Text,
  StyleSheet,
  Image,
  TouchableOpacity,
  FlatList,
  Alert,
  ActivityIndicator,
} from 'react-native';
import {useNavigation} from '@react-navigation/native';
import {IMAGE_CONSTANT, PrimaryColors} from '../../../Utils/Constants';
import strings from '../../../Utils/LocalizedStrings/LocalizedStrings';
import api from '../../../config/api';
import {SafeAreaView, SafeAreaProvider} from 'react-native-safe-area-context';
import IndexStyle from '../../../Theme/IndexStyle';
import {imgBaseUrl} from '../../../config/apiUrl';
import Toast from 'react-native-simple-toast';
import NavigationHeader from '../../../CommonComponents/NavigationHeader';
import Icon from 'react-native-vector-icons/Ionicons';
import Feather from 'react-native-vector-icons/Feather';
import { it } from 'node:test';
import { getWishlistData } from '../../../services/wishlistService';

const Wishlist = () => {
  const navigation = useNavigation<any>();
  const {styles, isDarkMode} = IndexStyle();
  const [classData, setClassData] = useState<any[]>([]);
  const [page, setPage] = useState(1);
  const [loading, setLoading] = useState(false);
  const [hasMoreData, setHasMoreData] = useState(true);

  const getWishList = async (pageNumber = 1) => {
    if (loading || !hasMoreData) {
      return;
    }
    setLoading(true);
    try {
      const data = await getWishlistData(pageNumber, 10);
      const newData = data.data?.items ?? [];
      if (pageNumber === 1) {
        setClassData(Array.isArray(newData) ? newData : []);
      } else {
        setClassData(prev =>
          Array.isArray(prev) && Array.isArray(newData)
            ? [...prev, ...newData]
            : Array.isArray(newData)
            ? [...newData]
            : [],
        );
      }
      if (!newData || newData.length === 0) {
        setHasMoreData(false);
      } else {
        setPage(prev => prev + 1);
      }
    } catch (err) {
      console.log('ERROR IN GET WHISHLIST DATA::', err);
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = (id: string) => {
    Alert.alert(
      strings.Wishlist.REMOVEFROMWISHLIST,
      strings.Wishlist.CONFIRMDELETE,
      [
        {text: 'Cancel', style: 'cancel'},
        {
          text: 'Delete',
          style: 'destructive',
          onPress: async () => {
            try {
              const response = await api.DeleteWhislistData.deleteWhislistData(
                id,
              );
              if (response.ok) {
                Toast.show(strings.Wishlist.REMOVEDMESSAGE, Toast.SHORT);
                setClassData([]);
                setPage(1);
                setHasMoreData(true);
                getWishList(1);
              } else {
                console.log('Failed to delete data. Status:', response.status);
              }
            } catch (err) {
              console.log('ERROR IN DELETE DATA::', err);
            }
          },
        },
      ],
      {cancelable: true},
    );
  };

  const renderItem = ({item}: {item: any}) => {
    console.log("WISHLIST ITME::::",item);
    
    const savedClass = item.savedClass;
    const classAbout = savedClass?.ClassAbout;
    const profilePhoto = classAbout?.profilePhoto;
    const imageUrl = profilePhoto ? `${imgBaseUrl}/${profilePhoto}` : null;
    const fullName = `${savedClass.firstName ?? ''} ${
      savedClass.lastName ?? ''
    }`;
    const className = savedClass.className ?? '';
    const rating = item.averageRating ?? 'N/A';
    const reviewCount = item.reviewCount ?? 'N/A';
    return (
      <TouchableOpacity onPress={()=>{navigation.navigate('TutorProfile', {classId: item.savedClassId})}}
        style={[
          BaseStyle.itemContainer,
          {backgroundColor: isDarkMode ? '#1B1B1B' : PrimaryColors.WHITE,borderColor: isDarkMode ? '#3E3E3E' : '#CCCCCC',},]}>
        <View style={[BaseStyle.imageWrapper, {width: '20%'}]}>
          <View style={BaseStyle.imageContainer}>
            {imageUrl ? (
              <Image
                source={{uri: imageUrl}}
                style={BaseStyle.image}
                resizeMode="cover"
              />
            ) : (
              <Icon name="person" size={50} color="#999" />
            )}
          </View>
        </View>
        <View style={[BaseStyle.infoWrapper, {width: '60%', marginLeft: '5%'}]}>
          <Text
            style={[
              BaseStyle.nameText,
              {color: isDarkMode ? PrimaryColors.WHITE : PrimaryColors.BLACK},
            ]}>
            {fullName}
          </Text>
          <Text style={{fontSize: 14, color: '#A3A3B9'}}> {className}</Text>
          <Text style={[BaseStyle.subText, {}]}>
            {/* right now wishlist dont have rating and review so right now comment out the code */}
            {/* <Image
              resizeMode="contain"
              source={IMAGE_CONSTANT.STAR}
              style={{height: 14, width: 14}}
            />
            <View style={{flexDirection: 'row', paddingTop: 5, paddingLeft: 4}}>
              <View style={{backgroundColor: '#FFEBF0', borderRadius: 8}}>
                <Text
                  style={{fontSize: 12,color: '#A3A3B9',}}>{` ${rating}.0 `}</Text>
              </View>
              <View style={{marginLeft: '2%'}}>
                <Text
                  style={{fontSize: 12,color: '#A3A3B9'}}>{`(${reviewCount} reviews)`}</Text>
              </View>
            </View> */}
          </Text>
        </View>
        <View
          style={{justifyContent: 'center', width: '15%', paddingLeft: '5%'}}>
          <TouchableOpacity onPress={() => handleDelete(item.id)}>
            <Feather name="trash" size={30} color={'#776D6D'} />
          </TouchableOpacity>
        </View>
      </TouchableOpacity>
    );
  };

  const renderEmptyComponent = () => {
    return (
      <View style={{marginTop: 60, alignItems: 'center'}}>
        <Text style={{fontSize: 16, color: '#999'}}>
          {strings.Wishlist.NOWISHLISTDATA}
        </Text>
      </View>
    );
  };

  useEffect(() => {
    getWishList(1);
  });

  return (
    <SafeAreaProvider
      style={[{backgroundColor: isDarkMode ? 'black' : 'white'}]}>
      {/* <NavigationHeader
        title={strings.Wishlist.WHISLIST}
        onBackPress={() => {
          navigation.navigate('Home');
        }}
      /> */}
      <NavigationHeader title="Wishlist" isBack={true} onBackPress={()=>{
          navigation.goBack();
        }}/>
      <SafeAreaView style={{flex: 1, width: '100%'}} edges={['left','right']}>
        <View style={{marginBottom: 50,}}>
          <FlatList
            data={classData}
            keyExtractor={item => item.id}
            renderItem={renderItem}
            contentContainerStyle={{padding: 16}}
            onEndReached={() => {
              if (!loading && hasMoreData) {
                getWishList(page);
              }
            }}
            onEndReachedThreshold={0.5}
            ListFooterComponent={
              loading ? (
                <View style={{paddingVertical: 16, alignItems: 'center'}}>
                  <ActivityIndicator
                    size="small"
                    color={
                      isDarkMode ? PrimaryColors.WHITE : PrimaryColors.BLACK
                    }
                  />
                </View>
              ) : null
            }
            ListEmptyComponent={!loading && renderEmptyComponent()}
          />
        </View>
      </SafeAreaView>
    </SafeAreaProvider>
  );
};

export default Wishlist;

const BaseStyle = StyleSheet.create({
  itemContainer: {
    flexDirection: 'row',
    borderRadius: 15,
    paddingHorizontal: 20,
    paddingVertical: 15,
    marginBottom: 12,
    alignItems: 'center',
    borderWidth: 1,
  },
  imageWrapper: {
    width: '25%',
    alignItems: 'center',
    justifyContent: 'center',
  },
  imageContainer: {
    height: 80,
    width: 80,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    overflow: 'hidden',
    backgroundColor: '#e6e6e6',
  },
  image: {
    height: '100%',
    width: '100%',
  },
  infoWrapper: {
    width: '50%',
    paddingLeft: 8,
    justifyContent: 'center',
  },
  nameText: {
    fontSize: 18,
    fontWeight: '700',
    marginBottom: 2,
  },
  subText: {
    fontSize: 15,
    fontWeight: '500',
  },
  buttonWrapper: {
    width: '25%',
    justifyContent: 'center',
    alignItems: 'center',
  },
  button: {
    width: '90%',
    borderWidth: 1,
    borderRadius: 8,
    paddingVertical: 6,
    alignItems: 'center',
    borderColor: '#ccc',
    backgroundColor: '#f9f9f9',
  },
  buttonText: {
    fontSize: 14,
    fontWeight: '600',
  },
});
