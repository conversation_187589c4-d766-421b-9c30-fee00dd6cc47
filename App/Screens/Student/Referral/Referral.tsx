import React, {useState} from 'react';
import {View, Text, StyleSheet, TouchableOpacity} from 'react-native';
import {PrimaryColors} from '../../../Utils/Constants';
import {useNavigation} from '@react-navigation/native';
import {SafeAreaView, SafeAreaProvider} from 'react-native-safe-area-context';
import Header from '../../../CommonComponents/Header';
import strings from '../../../Utils/LocalizedStrings/LocalizedStrings';
import IndexStyle from '../../../Theme/IndexStyle';
import {ScrollView} from 'react-native-gesture-handler';
import Icon from 'react-native-vector-icons/MaterialIcons';
import CommonTextInput from '../../../CommonComponents/CommonTextInput';
import CommonDateTimePicker from '../../../CommonComponents/CommonDateTimePicker';

const Referral = () => {
  const navigation = useNavigation();
  const {styles, isDarkMode} = IndexStyle();

  const [stdentReferralLink, setStdentReferralLink] = useState('');
  const [classsesReferralLink, setClassesReferralLink] = useState('');
  const [startDate, setStartDate] = useState('');
  const [endDate, setEndDate] = useState('');

  const formatDate = (dateString: string) => {
    if (!dateString) return '';
    const date = new Date(dateString);
    const day = String(date.getDate()).padStart(2, '0');
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const year = date.getFullYear();
    return `${day}/${month}/${year}`;
  };

  return (
    <SafeAreaProvider style={styles.container}>
      <Header title={strings.Refferal.REFERRAL} />
      <SafeAreaView style={{width: '100%', paddingHorizontal: '5%'}}>
        <View style={{marginLeft: '5%', marginBottom: '4%'}}>
          <Text style={[styles.label, {fontSize: 20}]}>
            {strings.Refferal.REFFERALDASHBOARD}
          </Text>
          <Text style={[styles.label, {fontSize: 14}]}>
            {strings.Refferal.SHAREUESTINWITHFRIENDSANDTRACKYOURREFERRALS}
          </Text>
        </View>
        <ScrollView style={{marginBottom: '50%'}}>
          <View
            style={[
              BaseStyle.ReferralCard,
              {backgroundColor: isDarkMode ? '#1c1c1e' : '#fff'},
            ]}>
            <View
              style={{flexDirection: 'row', justifyContent: 'space-between'}}>
              <Text style={[styles.label, {marginBottom: '5%'}]}>
                {strings.Refferal.TOTALREFERRALS}
              </Text>
              <Icon
                name="person"
                size={20}
                color={
                  isDarkMode ? PrimaryColors.WHITE : PrimaryColors.BLACK
                }></Icon>
            </View>
            <Text
              style={[
                styles.label,
                {color: isDarkMode ? PrimaryColors.WHITE : PrimaryColors.RED},
              ]}>
              0
            </Text>
          </View>

          <View
            style={[
              BaseStyle.ReferralCard,
              {backgroundColor: isDarkMode ? '#1c1c1e' : '#fff'},
            ]}>
            <View
              style={{flexDirection: 'row', justifyContent: 'space-between'}}>
              <Text style={[styles.label, {marginBottom: '5%'}]}>
                {strings.Refferal.STUDENTSREFERRED}
              </Text>
              <Icon
                name="person"
                size={20}
                color={
                  isDarkMode ? PrimaryColors.WHITE : PrimaryColors.BLACK
                }></Icon>
            </View>
            <Text
              style={[
                styles.label,
                {color: isDarkMode ? PrimaryColors.WHITE : PrimaryColors.BLUE},
              ]}>
              0
            </Text>
          </View>

          <View
            style={[
              BaseStyle.ReferralCard,
              {backgroundColor: isDarkMode ? '#1c1c1e' : '#fff'},
            ]}>
            <View
              style={{flexDirection: 'row', justifyContent: 'space-between'}}>
              <Text style={[styles.label, {marginBottom: '5%'}]}>
                {strings.Refferal.CLASSESREFERRED}
              </Text>
              <Icon
                name="person"
                size={20}
                color={
                  isDarkMode ? PrimaryColors.WHITE : PrimaryColors.BLACK
                }></Icon>
            </View>
            <Text
              style={[
                styles.label,
                {color: isDarkMode ? PrimaryColors.WHITE : PrimaryColors.GREEN},
              ]}>
              0
            </Text>
          </View>

          <View
            style={[
              BaseStyle.ReferralCard,
              {backgroundColor: isDarkMode ? '#1c1c1e' : '#fff'},
            ]}>
            <View style={{flexDirection: 'row'}}>
              <Icon
                name="share"
                size={20}
                style={{marginTop: '1%'}}
                color={
                  isDarkMode ? PrimaryColors.WHITE : PrimaryColors.BLACK
                }></Icon>
              <Text
                style={[
                  styles.label,
                  {fontSize: 20, marginBottom: '3%', marginLeft: '3%'},
                ]}>
                {strings.Refferal.MYREFERRALLINKS}
              </Text>
            </View>
            <Text style={[styles.label, {fontSize: 14, marginBottom: '3%'}]}>
              {strings.Refferal.SHARETHESELINKSTOREFERNNEWSTUDENTSANDCLASSES}
            </Text>

            <View style={{flexDirection: 'row'}}>
              <View style={{width: '70%'}}>
                <CommonTextInput
                  label={strings.Refferal.STUDENTREGISTRATIONLINK}
                  placeholder={strings.Refferal.STUDENTREGISTRATIONLINK}
                  value={stdentReferralLink}
                  onChangeText={setStdentReferralLink}
                />
              </View>
              <View
                style={{width: '20%', flexDirection: 'row', marginTop: '7%'}}>
                <TouchableOpacity
                  style={{
                    borderRadius: 12,
                    marginBottom: 20,
                    padding: 10,
                    shadowColor: '#000',
                    shadowOffset: {width: 0, height: 2},
                    shadowOpacity: 0.1,
                    shadowRadius: 4,
                    elevation: 3,
                    backgroundColor: isDarkMode ? '#1c1c1e' : '#fff',
                    marginRight: 10,
                  }}
                  onPress={() => {}}>
                  <Icon
                    name="content-copy"
                    size={20}
                    color={
                      isDarkMode ? PrimaryColors.WHITE : PrimaryColors.BLACK
                    }
                  />
                </TouchableOpacity>

                <TouchableOpacity
                  style={{
                    borderRadius: 12,
                    marginBottom: 20,
                    padding: 10,
                    shadowColor: '#000',
                    shadowOffset: {width: 0, height: 2},
                    shadowOpacity: 0.1,
                    shadowRadius: 4,
                    elevation: 3,
                    backgroundColor: isDarkMode ? '#1c1c1e' : '#fff',
                  }}
                  onPress={() => {}}>
                  <Icon
                    name="share"
                    size={20}
                    color={
                      isDarkMode ? PrimaryColors.WHITE : PrimaryColors.BLACK
                    }
                  />
                </TouchableOpacity>
              </View>
            </View>

            <View style={{flexDirection: 'row'}}>
              <View style={{width: '70%'}}>
                <CommonTextInput
                  label={strings.Refferal.CLASSREGISTRATIONLINK}
                  placeholder={strings.Refferal.CLASSREGISTRATIONLINK}
                  value={classsesReferralLink}
                  onChangeText={setClassesReferralLink}
                />
              </View>
              <View
                style={{width: '20%', flexDirection: 'row', marginTop: '7%'}}>
                <TouchableOpacity
                  style={{
                    borderRadius: 12,
                    marginBottom: 20,
                    padding: 10,
                    shadowColor: '#000',
                    shadowOffset: {width: 0, height: 2},
                    shadowOpacity: 0.1,
                    shadowRadius: 4,
                    elevation: 3,
                    backgroundColor: isDarkMode ? '#1c1c1e' : '#fff',
                    marginRight: 10,
                  }}
                  onPress={() => {}}>
                  <Icon
                    name="content-copy"
                    size={20}
                    color={
                      isDarkMode ? PrimaryColors.WHITE : PrimaryColors.BLACK
                    }
                  />
                </TouchableOpacity>

                <TouchableOpacity
                  style={{
                    borderRadius: 12,
                    marginBottom: 20,
                    padding: 10,
                    shadowColor: '#000',
                    shadowOffset: {width: 0, height: 2},
                    shadowOpacity: 0.1,
                    shadowRadius: 4,
                    elevation: 3,
                    backgroundColor: isDarkMode ? '#1c1c1e' : '#fff',
                  }}
                  onPress={() => {}}>
                  <Icon
                    name="share"
                    size={20}
                    color={
                      isDarkMode ? PrimaryColors.WHITE : PrimaryColors.BLACK
                    }
                  />
                </TouchableOpacity>
              </View>
            </View>
          </View>

          <View
            style={[
              BaseStyle.ReferralCard,
              {backgroundColor: isDarkMode ? '#1c1c1e' : '#fff'},
            ]}>
            <View style={{flexDirection: 'row'}}>
              <Icon
                name="history"
                size={20}
                style={{marginTop: '1%'}}
                color={
                  isDarkMode ? PrimaryColors.WHITE : PrimaryColors.BLACK
                }></Icon>
              <Text
                style={[
                  styles.label,
                  {fontSize: 20, marginBottom: '3%', marginLeft: '3%'},
                ]}>
                {strings.Refferal.REFERRALHISTORY}
              </Text>
            </View>
            <Text style={[styles.label, {fontSize: 14, marginBottom: '3%'}]}>
              {strings.Refferal.TRACKALLYOURSUCCESSFULREFERRALS}
            </Text>

            <View style={{flexDirection: 'row'}}>
              <CommonDateTimePicker
                style={{width: '35%', marginBottom: '2%', marginRight: '2%'}}
                label={strings.Refferal.STARTDATE}
                value={startDate}
                innerText={formatDate(startDate) || strings.Refferal.STARTDATE}
                onChange={val => {
                  setStartDate(val);
                }}
                mode="date"
              />
              <CommonDateTimePicker
                style={{width: '35%', marginBottom: '2%'}}
                label={strings.Refferal.ENDDATE}
                value={endDate}
                innerText={formatDate(endDate) || strings.Refferal.ENDDATE}
                onChange={val => {
                  setEndDate(val);
                }}
                mode="date"
              />
              <TouchableOpacity
                style={{
                  borderRadius: 12,
                  // marginBottom: 20,
                  // padding: 10,
                  shadowColor: '#000',
                  shadowOffset: {width: 0, height: 2},
                  shadowOpacity: 0.1,
                  shadowRadius: 4,
                  elevation: 3,
                  marginLeft: 10,
                  marginTop: '6%',
                  width: '20%',
                  height: 50,
                  backgroundColor: isDarkMode ? '#1c1c1e' : '#fff',
                }}
                onPress={() => {}}>
                <Text
                  style={[
                    styles.label,
                    {alignSelf: 'center', marginTop: '20%'},
                  ]}>
                  {strings.Refferal.FILTER}
                </Text>
              </TouchableOpacity>
            </View>
          </View>
          <View
            style={[
              BaseStyle.ReferralCard,
              {backgroundColor: isDarkMode ? '#1c1c1e' : '#fff'},
            ]}>
            <Text style={[styles.label, {marginBottom: '2%', fontSize: 15}]}>
              {strings.Refferal.NAME}
            </Text>
            <Text style={[styles.label, {marginBottom: '2%', fontSize: 15}]}>
              {strings.Refferal.EMAIL}
            </Text>
            <Text style={[styles.label, {marginBottom: '2%', fontSize: 15}]}>
              {strings.Refferal.TYPE}
            </Text>
            <Text style={[styles.label, {marginBottom: '2%', fontSize: 15}]}>
              {strings.Refferal.DATE}
            </Text>
          </View>
        </ScrollView>
      </SafeAreaView>
    </SafeAreaProvider>
  );
};

export default Referral;

const BaseStyle = StyleSheet.create({
  ReferralCard: {
    borderRadius: 12,
    marginHorizontal: 15,
    marginBottom: 20,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
});
