/* eslint-disable react-native/no-inline-styles */
import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  FlatList,
  Image,
} from 'react-native';
import { IMAGE_CONSTANT, PrimaryColors } from '../../../Utils/Constants';
import { SafeAreaView, SafeAreaProvider } from 'react-native-safe-area-context';
import Ionicons from 'react-native-vector-icons/Ionicons';
import IndexStyle from '../../../Theme/IndexStyle';
import strings from '../../../Utils/LocalizedStrings/LocalizedStrings';
import api from '../../../config/api';
import moment from 'moment';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { useNavigation } from '@react-navigation/native';

const Payment = () => {
  interface Transaction {
    id: string;
    type: 'CREDIT' | 'DEBIT';
    amount: number;
    reason: string;
    createdAt: string;
  }

  const [selectedTab, setSelectedTab] = useState('All');
  const [transactions, setTransactions] = useState<Transaction[]>([]);
  const [totalCoins, setTotalCoins] = useState(0);
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('desc');
  const [storedUserData, setStoredUserData] = useState<any>(null);
  const { isDarkMode } = IndexStyle();
  const navigation = useNavigation<any>();

  const addCoinsScreenNavigate = () => {
    navigation.navigate('AddCoins');
  };

  const fetchCoins = async () => {
    try {
      const response = await api.StudentCoin.getTotalCoins();
      const data = await response.json();
      console.log('hello from the fetch coin');
      console.log(response);
      if (response.ok) {
        setTotalCoins(data.coins);
      } else {
        console.error('Failed to fetch coins:', data);
      }
    } catch (error) {
      console.error('Error fetching coins:', error);
    }
  };

  const fetchTransactions = async () => {
    try {
      const response = await api.StudentCoin.getTransactionHistory();
      const data = await response.json();
      console.log('Transaction API response:', data);
      if (response.ok && Array.isArray(data)) {
        setTransactions(data);
      } else if (response.ok && Array.isArray(data.transactions)) {
        setTransactions(data.transactions);
      } else {
        console.error('Invalid transaction response format:', data);
      }
    } catch (error) {
      console.error('Error fetching transactions:', error);
    }
  };

  const fetchStoredUserData = async () => {
    try {
      const userDataString = await AsyncStorage.getItem('userData');
      console.log('Raw AsyncStorage userData:', userDataString);
      if (userDataString) {
        const userData = JSON.parse(userDataString);
        console.log('Parsed userData:', userData);
        setStoredUserData(userData.data);
      } else {
        console.log('No userData found in AsyncStorage');
      }
    } catch (error) {
      console.error('Error retrieving userData from AsyncStorage:', error);
    }
  };

  useEffect(() => {
    fetchCoins();
    fetchTransactions();
    fetchStoredUserData();
  }, []);

  const filteredTransactions =
    selectedTab === 'All'
      ? transactions
      : transactions.filter(
          (tx) => tx.type.toUpperCase() === selectedTab.toUpperCase(),
        );

  const sortedTransactions = [...filteredTransactions].sort((a, b) => {
    const dateA = new Date(a.createdAt).getTime();
    const dateB = new Date(b.createdAt).getTime();
    return sortDirection === 'asc' ? dateA - dateB : dateB - dateA;
  });

  const toggleSortDirection = () => {
    setSortDirection((prev) => (prev === 'asc' ? 'desc' : 'asc'));
  };

  const renderTransactionItem = ({ item }: { item: Transaction }) => {
    const isCredit = item.type === 'CREDIT';
    const formattedDate = moment(item.createdAt).format('DD MMM YYYY, HH:mm');

    return (
      <View
        style={{
          marginHorizontal: 16,
          marginVertical: 8,
          padding: 16,
        }}>
        <View style={{ flexDirection: 'row', justifyContent: 'space-between' }}>
          <View style={{ flex: 1 }}>
            <Text
              style={{
                fontSize: 16,
                fontWeight: 'bold',
                color: isDarkMode ? '#fff' : '#000',
              }}>
              {isCredit ? 'Credited' : 'Debited'}
            </Text>
            <Text
              style={{
                fontSize: 14,
                color: isDarkMode ? '#ccc' : '#444',
                marginTop: 4,
                fontWeight: '500',
              }}>
              Reason: {item.reason}
            </Text>
            <Text
              style={{
                fontSize: 13,
                color: '#888',
                marginTop: 4,
              }}>
              {formattedDate}
            </Text>
          </View>

          <View style={{ justifyContent: 'center', alignItems: 'flex-end' }}>
            <Text
              style={{
                fontSize: 16,
                fontWeight: 'bold',
                color: isCredit ? '#2E8B57' : '#D32F2F',
              }}>
              {isCredit ? '+ ' : '- '}
              {item.amount} Coins
            </Text>
          </View>
        </View>
      </View>
    );
  };

  const firstName = storedUserData?.user?.firstName ?? 'Guest';
  const lastName = storedUserData?.user?.lastName ?? '';

  return (
    <SafeAreaProvider>
      <SafeAreaView
        style={{
          flex: 1,
          backgroundColor: PrimaryColors.BLACK,
        }}
        edges={['left', 'right']}>
        <View
          style={{
            flexDirection: 'row',
            alignItems: 'center',
            justifyContent: 'space-between',
            height: 100,
            paddingHorizontal: 20,
            backgroundColor: PrimaryColors.BLACK,
            paddingTop: '6%',
          }}>
          {/* Back Button */}
          <TouchableOpacity
            onPress={() => navigation.goBack()}
            style={{ padding: 8 }}>
            <Ionicons name="arrow-back" size={24} color={PrimaryColors.WHITE} />
          </TouchableOpacity>

          {/* Title */}
          <Text
            style={{
              flex: 1,
              textAlign: 'center',
              fontSize: 24,
              color: PrimaryColors.WHITE,
              fontWeight: '600',
              marginRight: 32,
            }}>
            Uest Coins
          </Text>

          {/* Placeholder for spacing */}
          <View style={{ width: 24 }} />
        </View>

        <View
          style={{
            flex: 1, 
          }}>
          <View
            style={{
              paddingHorizontal: 20,
              paddingBottom: 24,
              backgroundColor: PrimaryColors.BLACK,
            }}>
            <View
              style={{
                borderRadius: 20,
                paddingVertical: 24,
                paddingHorizontal: 20,
                alignItems: 'center',
                backgroundColor: '#1D1D1D',
                borderWidth: 3,
              }}>
              <Text
                style={{
                  color: PrimaryColors.GRAYSHADOW,
                  fontSize: 12,
                }}>
                Main Balance
              </Text>
              <Text
                style={{
                  color: PrimaryColors.WHITE,
                  fontSize: 38,
                  marginVertical: 4,
                }}>
                {totalCoins} Coins
              </Text>

              <TouchableOpacity
                style={{
                  flexDirection: 'row',
                  alignItems: 'center',
                  paddingHorizontal: 12,
                  paddingVertical: 8,
                  borderRadius: 10,
                }}
                onPress={addCoinsScreenNavigate}>
                <Ionicons
                  name="add-circle-outline"
                  size={18}
                  color={PrimaryColors.WHITE}
                />
                <Text
                  style={{
                    color: PrimaryColors.WHITE,
                    marginLeft: 6,
                    fontSize: 16,
                  }}>
                  {strings.PaymentList.ADDCOINS}
                </Text>
              </TouchableOpacity>
            </View>
          </View>

          <View
            style={{
              flex: 1,
              backgroundColor: isDarkMode ? PrimaryColors.BLACK : PrimaryColors.WHITE,
              borderTopLeftRadius: 20,
              borderTopRightRadius: 20,
            }}>
            <View
              style={{
                flexDirection: 'row',
                width: '100%',
                paddingHorizontal: '4%',
                paddingTop: 16,
                justifyContent: 'space-between',
                borderTopLeftRadius: 20,
              }}>
              {['All', 'Credit', 'Debit'].map((tab) => (
                <TouchableOpacity
                  key={tab}
                  style={{
                    width: '18%',
                    justifyContent: 'center',
                    alignItems: 'center',
                    borderRadius: 10,
                    borderWidth: 1,
                    borderColor: PrimaryColors.LIGHTGRAY,
                    ...(selectedTab === tab && {
                      backgroundColor: '#FDC5A3',
                    }),
                  }}
                  onPress={() => setSelectedTab(tab)}>
                  <Text
                    style={{
                      fontSize: 14,
                      color: isDarkMode ? '#fff' : '#000',
                      ...(selectedTab === tab && {
                        color: PrimaryColors.BLACK,
                        fontWeight: '600',
                      }),
                    }}>
                    {tab}
                  </Text>
                </TouchableOpacity>
              ))}
              <TouchableOpacity
                style={{
                  flexDirection: 'row',
                  justifyContent: 'center',
                  alignItems: 'center',
                  paddingVertical: '2%',
                  paddingHorizontal: '2%',
                  borderRadius: 10,
                  borderWidth: 1,
                  borderColor: PrimaryColors.LIGHTGRAY,
                }}
                onPress={toggleSortDirection}>
                <Ionicons
                  name="funnel-outline"
                  size={16}
                  color={isDarkMode ? '#fff' : '#000'}
                />
                <Text
                  style={{
                    fontSize: 14,
                    marginLeft: 6,
                    color: isDarkMode ? '#fff' : '#000',
                  }}>
                  Sort By Date {sortDirection === 'asc' ? '↑' : '↓'}
                </Text>
              </TouchableOpacity>
            </View>

            <View
              style={{
                flexDirection: 'row',
                justifyContent: 'space-between',
                paddingHorizontal: 20,
                marginTop: 24,
                marginBottom: 6,
              }}>
              <Text
                style={{
                  fontSize: 20,
                  fontWeight: '600',
                  color: isDarkMode ? PrimaryColors.WHITE : PrimaryColors.BLACK,
                }}>
                Latest Transactions
              </Text>
              <TouchableOpacity>
                <Text
                  style={{
                    fontSize: 16,
                    color: '#929292',
                  }}>
                  View all
                </Text>
              </TouchableOpacity>
            </View>

            {sortedTransactions.length === 0 ? (
              <View
                style={{
                  alignItems: 'center',
                  marginTop: 40,
                  paddingHorizontal: 20,
                  flex: 1,
                }}>
                <Image
                  resizeMode="contain"
                  source={IMAGE_CONSTANT.UESTCOIN}
                  style={{
                    width: 200,
                    height: 100,
                  }}
                />
                <Text
                  style={{
                    fontSize: 15,
                    textAlign: 'center',
                    marginTop: 10,
                    color: isDarkMode ? '#9E9E9E' : '#777',
                  }}>
                  {strings.PaymentList.NOTRANSACTIONFOUND}
                </Text>
              </View>
            ) : (
              <FlatList
              showsVerticalScrollIndicator={false}
                style={{ flex: 1 }} 
                data={sortedTransactions}
                keyExtractor={(item) => item.id}
                ItemSeparatorComponent={() => (
                  <View
                    style={{
                      height: 2,
                      marginHorizontal: 20,
                      backgroundColor: isDarkMode ? '#444' : '#E0E0E0',
                    }}
                  />
                )}
                contentContainerStyle={{ paddingBottom: 20 }}
                renderItem={renderTransactionItem}
              />
            )}
          </View>
        </View>
      </SafeAreaView>
    </SafeAreaProvider>
  );
};

export default Payment;