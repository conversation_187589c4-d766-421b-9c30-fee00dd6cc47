import React, {useEffect, useState} from 'react';
import {View, Text, StyleSheet, TouchableOpacity, Image} from 'react-native';
import {IMAGE_CONSTANT, PrimaryColors} from '../../../Utils/Constants';
import {useNavigation, useRoute} from '@react-navigation/native';
import {SafeAreaView, SafeAreaProvider} from 'react-native-safe-area-context';
import {useSelector} from 'react-redux';
import {RootState} from '../../../Redux/store';
import LinearGradient from 'react-native-linear-gradient';
import strings from '../../../Utils/LocalizedStrings/LocalizedStrings';
import {ScrollView} from 'react-native-gesture-handler';
import Ionicons from 'react-native-vector-icons/Ionicons';
import LinearProgressBar from '../../../CommonComponents/LinearProgressBar';
import BellAnimation from '../../../CommonComponents/BellAnimation';
import { getResultData } from '../../../services/resultService';
import IndexStyle from '../../../Theme/IndexStyle';
import Header from '../../../CommonComponents/Header';
const ViewResult = () => {
  const navigation = useNavigation();
  const {styles, isDarkMode} = IndexStyle();

  const [imageError, setImageError] = useState(false);
  const [resultData, setResulteData] = useState<any>();

  const route = useRoute<any>();
  const {examId, examName} = route.params;

  useEffect(() => {
    getResultDataHandler();
  }, []);
  useEffect(() => {
    console.log('UPDATED RESULT DATA::', resultData);
  }, [resultData]);

  // const staticData = [
  //   {
  //     rank: 1,
  //     userId: 'b3711a5f-2da5-4126-aa7c-d2a11a99a659',
  //     name: 'Vivek Kalola ',
  //     classesName: 'Vivek Tution Classes ',
  //     classesLogo:
  //       'uploads/classes/b3711a5f-2da5-4126-aa7c-d2a11a99a659/photos/cropped-image-1745561006549-497631614.jpg',
  //     score: 77,
  //     attempted: 100,
  //     totalQuestions: 100,
  //   },
  //   {
  //     rank: 2,
  //     userId: '84084bc5-7b28-4256-812e-62ad5c636e40',
  //     name: 'Milan Chhatrola ',
  //     classesName: 'Dark Horse ',
  //     classesLogo:
  //       'uploads/classes/84084bc5-7b28-4256-812e-62ad5c636e40/photos/cropped-image-1745057413416-475750446.jpg',
  //     score: 76,
  //     attempted: 98,
  //     totalQuestions: 100,
  //   },
  //   {
  //     rank: 3,
  //     userId: '686fbf25-896b-48d6-8e99-d0143b559cc1',
  //     name: 'Liza Patel ',
  //     classesName: 'Liza Tution Classes ',
  //     classesLogo:
  //       'uploads/classes/686fbf25-896b-48d6-8e99-d0143b559cc1/photos/cropped-image-1745293359696-3911120.jpg',
  //     score: 76,
  //     attempted: 100,
  //     totalQuestions: 100,
  //   },
  //   {
  //     rank: 3,
  //     userId: '9e9e5ec1-a38b-40bb-bed0-6d01de66f501',
  //     name: 'Rahul Joshi ',
  //     classesName: 'Joshi Tution ',
  //     classesLogo:
  //       'uploads/classes/9e9e5ec1-a38b-40bb-bed0-6d01de66f501/photos/cropped-image-1745390871285-573219181.jpg',
  //     score: 76,
  //     attempted: 100,
  //     totalQuestions: 100,
  //   },
  //   {
  //     rank: 3,
  //     userId: '4d02c6a4-de4f-4482-b9d6-fd4f86587d0e',
  //     name: 'Rathod Bharat ',
  //     classesName: 'Genius tuition classes ',
  //     classesLogo:
  //       'uploads/classes/4d02c6a4-de4f-4482-b9d6-fd4f86587d0e/photos/cropped-image-1744867805601-174513004.jpg',
  //     score: 76,
  //     attempted: 100,
  //     totalQuestions: 100,
  //   },
  //   {
  //     rank: 4,
  //     userId: 'a2856fb6-53ff-4039-bc18-da35a71e4e06',
  //     name: 'Vaibhavi  Vaghadiya  ',
  //     classesName: 'Vaibhavi Tution Classes ',
  //     classesLogo:
  //       'uploads/classes/a2856fb6-53ff-4039-bc18-da35a71e4e06/photos/cropped-image-1744860134670-130401636.jpg',
  //     score: 75,
  //     attempted: 100,
  //     totalQuestions: 100,
  //   },
  //   {
  //     rank: 5,
  //     userId: 'e698288a-560d-48d9-bfe3-0705e330cdf9',
  //     name: 'Dabhi  Prashant  ',
  //     classesName: 'DP classes ',
  //     classesLogo:
  //       'uploads/classes/e698288a-560d-48d9-bfe3-0705e330cdf9/photos/cropped-image-1745569432757-294765661.jpg',
  //     score: 74,
  //     attempted: 100,
  //     totalQuestions: 100,
  //   },
  //   {
  //     rank: 6,
  //     userId: 'ad956269-dc3e-495e-8b60-bc2a0e96aaba',
  //     name: 'Jitendra  Sir ',
  //     classesName: 'Active Calsses',
  //     classesLogo:
  //       'uploads/classes/ad956269-dc3e-495e-8b60-bc2a0e96aaba/photos/cropped-image-1745128667358-35386740.jpg',
  //     score: 73,
  //     attempted: 100,
  //     totalQuestions: 100,
  //   },
  //   {
  //     rank: 7,
  //     userId: '4093721f-c158-46ff-a988-0014c4850939',
  //     name: 'Krupa Vaghadiya  ',
  //     classesName: 'Krupa Tution Classes ',
  //     classesLogo:
  //       'uploads/classes/4093721f-c158-46ff-a988-0014c4850939/photos/cropped-image-1744859565621-935801275.jpg',
  //     score: 72,
  //     attempted: 99,
  //     totalQuestions: 100,
  //   },
  //   {
  //     rank: 8,
  //     userId: '9cb33702-e3db-4f33-a39f-39c2cee3f9fd',
  //     name: 'Hiren Kotadiya ',
  //     classesName: 'Hk tuition classes',
  //     classesLogo:
  //       'uploads/classes/9cb33702-e3db-4f33-a39f-39c2cee3f9fd/photos/1000588577-1744734981329-282259573.jpg',
  //     score: 72,
  //     attempted: 100,
  //     totalQuestions: 100,
  //   },
  //   {
  //     rank: 8,
  //     userId: '5d61d553-cafa-42c4-86bf-c3b82b306648',
  //     name: 'Janki Sherasiya ',
  //     classesName: 'JS learning hub',
  //     classesLogo:
  //       'uploads/classes/5d61d553-cafa-42c4-86bf-c3b82b306648/photos/cropped-image-1745639904797-195808685.jpg',
  //     score: 72,
  //     attempted: 100,
  //     totalQuestions: 100,
  //   },
  //   {
  //     rank: 9,
  //     userId: '8549e43b-5bf7-4e1e-92cc-2ce69d79a0ca',
  //     name: 'Drashti  Bhanushali ',
  //     classesName: 'Drashti Classes ',
  //     classesLogo:
  //       'uploads/classes/8549e43b-5bf7-4e1e-92cc-2ce69d79a0ca/photos/cropped-image-1745474296577-826751560.jpg',
  //     score: 71,
  //     attempted: 100,
  //     totalQuestions: 100,
  //   },
  //   {
  //     rank: 9,
  //     userId: '4f8606a8-b356-4803-afa3-df2e321438fd',
  //     name: 'Jayesh  Pujara ',
  //     classesName: 'Jayesh Classes ',
  //     classesLogo:
  //       'uploads/classes/4f8606a8-b356-4803-afa3-df2e321438fd/photos/cropped-image-1745560748651-460222194.jpg',
  //     score: 71,
  //     attempted: 100,
  //     totalQuestions: 100,
  //   },
  //   {
  //     rank: 9,
  //     userId: '8114bc01-85de-4212-9557-01d634925782',
  //     name: 'Manish Sir ',
  //     classesName: 'Excellent Classes',
  //     classesLogo:
  //       'uploads/classes/8114bc01-85de-4212-9557-01d634925782/photos/cropped-image-1744882662183-416276649.jpg',
  //     score: 71,
  //     attempted: 100,
  //     totalQuestions: 100,
  //   },
  //   {
  //     rank: 9,
  //     userId: '86d22e7d-0216-4cc4-95e9-bf544e81ae44',
  //     name: 'Nikita Chavda ',
  //     classesName: 'Nikita Classes ',
  //     classesLogo:
  //       'uploads/classes/86d22e7d-0216-4cc4-95e9-bf544e81ae44/photos/cropped-image-1745636958091-797646395.jpg',
  //     score: 71,
  //     attempted: 100,
  //     totalQuestions: 100,
  //   },
  //   {
  //     rank: 10,
  //     userId: 'e06f5feb-e143-4e40-b898-1de3aebef8c0',
  //     name: 'Yukti Kaneriya ',
  //     classesName: 'YK classes ',
  //     classesLogo:
  //       'uploads/classes/e06f5feb-e143-4e40-b898-1de3aebef8c0/photos/cropped-image-1745640861616-119398948.jpg',
  //     score: 70,
  //     attempted: 99,
  //     totalQuestions: 100,
  //   },
  // ];

  const getResultDataHandler = async () => {
    try {
      const data = await getResultData(examId);
      setResulteData(data);
      setImageError(false);
    } catch (err) {
      console.log('ERROR IN GET RESULT DATA::', err);
    }
  };

  return (
    <SafeAreaProvider style={styles.container}>
      <Header title={examName} />

      <SafeAreaView style={BaseStyle.content}>
        <Text
          style={{
            fontSize: 25,
            fontWeight: '700',
            color: isDarkMode ? PrimaryColors.WHITE : PrimaryColors.ORANGE,
            marginBottom: 20,
          }}>
          {strings.ViewResult.EXAMRANKING}
        </Text>

        <ScrollView
          style={{width: '100%',}}
          showsVerticalScrollIndicator={false}>
          {resultData?.map((item: any, index: number) => (
            <View
              key={index}
              style={[
                {
                  flexDirection: 'row',
                  alignItems: 'center',
                  backgroundColor: index === 0 ? '#FFF2E6' : '#FFFFFF',
                  borderRadius: 20,
                  borderWidth: 0.5,
                  padding: 12,
                  marginBottom: 16,
                }
              ]}>
              <View
                style={{
                  backgroundColor: PrimaryColors.CARDBORDER,
                  borderRadius: 20,
                  width: 40,
                  height: 40,
                  justifyContent: 'center',
                  alignItems: 'center',
                  marginRight: 10,
                }}>
                <Text style={{color: PrimaryColors.WHITE, fontWeight: 'bold'}}>
                  #{item.rank}
                </Text>
              </View>

              <Image
                source={{uri: `https://www.uest.in/uapi/${item.classesLogo}`}}
                style={{
                  width: 50,
                  height: 50,
                  borderWidth: 0.5,
                  borderRadius: 10,
                  marginRight: 12,
                  borderColor: PrimaryColors.CARDBORDER,
                }}
              />

              <View style={{flex: 1}}>
                <Text style={{fontSize: 16, fontWeight: '700', color: PrimaryColors.BLACK}}>
                  {item.name}
                </Text>
                <Text style={[{fontSize: 13, color: PrimaryColors.LIGHTGRAY}]}>
                  ({item.classesName})
                </Text>

                {index === 0 && (
                  <View
                    style={{
                      flexDirection: 'row',
                      alignItems: 'center',
                      marginTop: 4,
                    }}>
                    <Text
                      style={{fontSize: 14, color: PrimaryColors.CARDBORDER}}>
                         🏆 ₹21,000
                      {/* 🏆 ₹21,000 this is the static data as server dont have this key as part of response */}
                    </Text>
                  </View>
                )}
              </View>

              <View
                style={{
                  backgroundColor: '#d4ffe3',
                  paddingHorizontal: 10,
                  paddingVertical: 6,
                  borderRadius: 20,
                  marginTop: 8,
                  alignSelf: 'flex-start',
                }}>
                <Text style={{color: 'green', fontWeight: '600'}}>
                  {item.score}/{item.totalQuestions}
                </Text>
              </View>
            </View>
          ))}
        </ScrollView>
      </SafeAreaView>
    </SafeAreaProvider>
  );
};

export default ViewResult;
const BaseStyle = StyleSheet.create({
  content: {
    flex: 1,
    alignItems: 'center',
    width: '100%',
    paddingHorizontal: '20%',
  },
});
