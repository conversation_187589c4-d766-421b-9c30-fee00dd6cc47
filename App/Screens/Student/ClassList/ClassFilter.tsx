/* eslint-disable react-native/no-inline-styles */
import React, {useState, useEffect} from 'react';
import {
  Modal,
  Text,
  View,
  ScrollView,
  TouchableOpacity,
  Platform,
  Dimensions,
} from 'react-native';
import CommonDropdownPicker from '../../../CommonComponents/CommonDropdownPicker';
import {PrimaryColors} from '../../../Utils/Constants';
import strings from '../../../Utils/LocalizedStrings/LocalizedStrings';
import api from '../../../config/api';
import IndexStyle from '../../../Theme/IndexStyle';

interface ClassFilterProps {
  onApply: (filters: {
    category: string;
    boardType: string;
    medium: string;
    section: string;
    subject: string;
    coachingType: string;
  }) => void;
  currentFilters: {
    category: string;
    boardType: string;
    medium: string;
    section: string;
    subject: string;
    coachingType: string;
  };
  modalVisible: boolean;
  setModalVisible: (visible: boolean) => void;
  title: string;
  applyLabel?: string;
}

interface Constant {
  id: string;
  name: string;
  details: {id: string; value: string}[];
}

const ClassFilter: React.FC<ClassFilterProps> = ({
  onApply,
  currentFilters,
  modalVisible,
  setModalVisible,
  title = strings.ClassCategoryList.FILTERCLASSES,
  applyLabel = strings.ClassCategoryList.APPLY,
}) => {
  const [filters, setFilters] = useState({
    ...currentFilters,
    details: '',
  });
  const {styles, isDarkMode} = IndexStyle();

  const [constants, setConstants] = useState<Constant[]>([]);

  useEffect(() => {
    const fetchConstants = async () => {
      try {
        const res = await api.GetClassCategoryList.getClassCategoryList();
        const text = await res.text();
        const jsondata = JSON.parse(text);
        setConstants(jsondata);
        console.log('PARSED JSON DATA::', jsondata);
      } catch (err) {
        console.log('ERROR IN GET CLASS CATEGORY LIST::', err);
      }
    };
    fetchConstants();
  }, []);

  const categories = constants
    .filter(item =>
      [
        'Education',
        'Drama',
        'Music',
        'Art & Craft',
        'Sports',
        'Languages',
        'Technology',
        'Arts',
      ].includes(item.name),
    )
    .map(item => ({label: item.name, value: item.name}));
  const boardTypes = (
    constants.find(item => item.name === 'Board Type')?.details || []
  ).map(item => ({
    label: item.value,
    value: item.value,
  }));
  const mediums = (
    constants.find(item => item.name === 'Medium')?.details || []
  ).map(item => ({
    label: item.value,
    value: item.value,
  }));
  const sections = (
    constants.find(item => item.name === 'Section')?.details || []
  ).map(item => ({
    label: item.value,
    value: item.value,
  }));
  const subjects = (
    constants.find(item => item.name === 'Subject')?.details || []
  ).map(item => ({
    label: item.value,
    value: item.value,
  }));
  const coachingTypes = (
    constants.find(item => item.name === 'Coaching Type')?.details || []
  ).map(item => ({
    label: item.value,
    value: item.value,
  }));
  const detailsOptions = (
    constants.find(item => item.name === filters.category)?.details || []
  ).map(item => ({
    label: item.value,
    value: item.value,
  }));

  const handleFilterChange = (key: keyof typeof filters, value: string) => {
    setFilters(prev => ({
      ...prev,
      [key]: value,
      ...(key === 'category' && value !== 'Education'
        ? {
            boardType: '',
            medium: '',
            section: '',
            subject: '',
          }
        : {}),
      ...(key === 'category' ? {details: ''} : {}),
    }));
  };

  const resetFilters = () => {
    setFilters({
      category: '',
      boardType: '',
      medium: '',
      section: '',
      subject: '',
      coachingType: '',
      details: '',
    });
  };
  const screenHeight = Dimensions.get('window').height;

  return (
    <Modal
      animationType="fade"
      transparent={true}
      visible={modalVisible}
      onRequestClose={() => setModalVisible(false)}>
      <View
        style={[
          styles.container,
          {justifyContent: 'center', backgroundColor: 'rgba(0, 0, 0, 0.5)'},
        ]}>
        <View
          style={[
            styles.modalView,
            {
              width: '90%',
              ...(Platform.OS === 'ios' && {
                maxHeight: screenHeight * 0.8,
              }),
              backgroundColor: isDarkMode ? PrimaryColors.BLACK : PrimaryColors.WHITE,
            },
          ]}>
          <Text
            style={{
              fontSize: 20,
              fontWeight: '700',
              marginBottom: 20,
              textAlign: 'center',
              color: isDarkMode ? PrimaryColors.WHITE : PrimaryColors.BLACK,
            }}>
            {title}
          </Text>
          <ScrollView
            style={{flexGrow: 0}}
            contentContainerStyle={{paddingBottom: 20}}
            showsVerticalScrollIndicator={false}>
            <View style={{marginBottom: 16}}>
              <CommonDropdownPicker
                label={strings.ClassCategoryList.CATEGORY}
                data={categories}
                value={filters.category}
                onChange={(value: string) =>
                  handleFilterChange('category', value)
                }
                placeholder={strings.ClassCategoryList.SELECTCATEGORY}
                style={{
                  width: '100%',
                  color: isDarkMode ? PrimaryColors.WHITE : PrimaryColors.BLACK,
                }}
              />
            </View>

            {filters.category && filters.category !== 'Education' && (
              <View style={{marginBottom: 16}}>
                <CommonDropdownPicker
                  label={strings.ClassCategoryList.DETAILS}
                  data={detailsOptions}
                  value={filters.details}
                  onChange={(value: string) =>
                    handleFilterChange('details', value)
                  }
                  placeholder={strings.ClassCategoryList.SELECTDETAILS}
                  style={{
                    width: '100%',
                    color: isDarkMode
                      ? PrimaryColors.WHITE
                      : PrimaryColors.BLACK,
                  }}
                />
              </View>
            )}

            {(!filters.category || filters.category === 'Education') && (
              <>
                <View style={{marginBottom: 16}}>
                  <CommonDropdownPicker
                    label={strings.ClassCategoryList.BOARDTYPE}
                    data={boardTypes}
                    value={filters.boardType}
                    onChange={(value: string) =>
                      handleFilterChange('boardType', value)
                    }
                    placeholder={strings.ClassCategoryList.SELECTBOARDTYPE}
                    style={{
                      width: '100%',
                      color: isDarkMode
                        ? PrimaryColors.WHITE
                        : PrimaryColors.BLACK,
                    }}
                  />
                </View>
                <View style={{marginBottom: 16}}>
                  <CommonDropdownPicker
                    label={strings.ClassCategoryList.MEDIUM}
                    data={mediums}
                    value={filters.medium}
                    onChange={(value: string) =>
                      handleFilterChange('medium', value)
                    }
                    placeholder={strings.ClassCategoryList.SELECTMEDIUM}
                    style={{
                      width: '100%',
                      color: isDarkMode
                        ? PrimaryColors.WHITE
                        : PrimaryColors.BLACK,
                    }}
                  />
                </View>
                <View style={{marginBottom: 16}}>
                  <CommonDropdownPicker
                    label={strings.ClassCategoryList.SECTION}
                    data={sections}
                    value={filters.section}
                    onChange={(value: string) =>
                      handleFilterChange('section', value)
                    }
                    placeholder={strings.ClassCategoryList.SELECTSECTION}
                    style={{
                      width: '100%',
                      color: isDarkMode
                        ? PrimaryColors.WHITE
                        : PrimaryColors.BLACK,
                    }}
                  />
                </View>
                <View style={{marginBottom: 16}}>
                  <CommonDropdownPicker
                    label={strings.ClassCategoryList.SUBJECT}
                    data={subjects}
                    value={filters.subject}
                    onChange={(value: string) =>
                      handleFilterChange('subject', value)
                    }
                    placeholder={strings.ClassCategoryList.SELECTSUBJECT}
                    style={{
                      width: '100%',
                      color: isDarkMode
                        ? PrimaryColors.WHITE
                        : PrimaryColors.BLACK,
                    }}
                  />
                </View>
              </>
            )}

            <View style={{marginBottom: 16}}>
              <CommonDropdownPicker
                label={strings.ClassCategoryList.COACHINGTYPE}
                data={coachingTypes}
                value={filters.coachingType}
                onChange={(value: string) =>
                  handleFilterChange('coachingType', value)
                }
                placeholder={strings.ClassCategoryList.SELECTCOACHINGTYPE}
                style={{
                  width: '100%',
                  color: isDarkMode ? PrimaryColors.WHITE : PrimaryColors.BLACK,
                }}
              />
            </View>

            <View
              style={{
                flexDirection: 'row',
                justifyContent: 'flex-end',
                marginTop: 20,
                paddingBottom: 10,
              }}>
              <TouchableOpacity
                style={{
                  borderRadius: 10,
                  paddingVertical: 12,
                  paddingHorizontal: 24,
                  elevation: 3,
                  backgroundColor: isDarkMode
                    ? '#1B1B1B'
                    : PrimaryColors.BLACK,
                }}
                onPress={resetFilters}>
                <Text
                  style={{
                    color: PrimaryColors.WHITE,
                    fontWeight: '600',
                    fontSize: 16,
                  }}>
                  {strings.ClassCategoryList.RESET}
                </Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={{
                  borderRadius: 10,
                  paddingVertical: 12,
                  paddingHorizontal: 24,
                  elevation: 3,
                  backgroundColor: PrimaryColors.ORANGE,
                  marginLeft: 12,
                }}
                onPress={() => {
                  onApply(filters);
                  setModalVisible(false);
                }}>
                <Text
                  style={{
                    color: PrimaryColors.WHITE,
                    fontWeight: '600',
                    fontSize: 16,
                  }}>
                  {applyLabel}
                </Text>
              </TouchableOpacity>
            </View>
          </ScrollView>
        </View>
      </View>
    </Modal>
  );
};

export default ClassFilter;
