/* eslint-disable react-native/no-inline-styles */
import React, {useEffect, useState} from 'react';
import {
  View,
  Text,
  StyleSheet,
  Image,
  TouchableOpacity,
  FlatList,
  ActivityIndicator,
} from 'react-native';
import {TextInput} from 'react-native-gesture-handler';
import {IMAGE_CONSTANT, PrimaryColors} from '../../../Utils/Constants';
import {useNavigation, useRoute} from '@react-navigation/native';
import api from '../../../config/api';
import {SafeAreaView, SafeAreaProvider} from 'react-native-safe-area-context';
import Ionicons from 'react-native-vector-icons/Ionicons';
import Icon from 'react-native-vector-icons/Ionicons';
import IndexStyle from '../../../Theme/IndexStyle';
import ClassFilter from './ClassFilter';
import {imgBaseUrl} from '../../../config/apiUrl';
import Feather from 'react-native-vector-icons/Feather';

const ClassList = () => {
  const navigation = useNavigation<any>();
  const route = useRoute<any>();
    const {category} = route.params ? route.params : '';
    // console.log("CATEGORY FROM NAVIGATION:::",category);
  const [searchText, setSearchText] = useState('');
  const [classData, setClassData] = useState<any[]>([]);
  const [isFilterModalVisible, setFilterModalVisible] = useState(false);
  const [filters, setFilters] = useState({
    category: category ? category : '',
    boardType: '',
    medium: '',
    section: '',
    subject: '',
    coachingType: '',
  });
  const [page, setPage] = useState(1);
  const [loading, setLoading] = useState(false);
  const [hasMoreData, setHasMoreData] = useState(true);
  const [filteredClassData, setFilteredClassData] = useState<any[]>([]);
  const {isDarkMode} = IndexStyle();

  const formatArrayToString = (value: any) => {
    if (Array.isArray(value)) {
      return value.join(', ');
    }
    if (typeof value === 'string') {
      try {
        const parsed = JSON.parse(value);
        if (Array.isArray(parsed)) {
          return parsed.join(', ');
        }
      } catch {
        return value;
      }
    }
    return '';
  };

  useEffect(() => {
    setPage(1);
    setHasMoreData(true);
    setClassData([]);
    getClassList(1);
  }, []);
  useEffect(() => {}, [filters]);

  console.log('', filters);
  useEffect(() => {
    const lowerSearch = searchText.trim().toLowerCase();
    const filtered = classData.filter(item => {
      const fullName = `${item.firstName ?? ''} ${
        item.lastName ?? ''
      }`.toLowerCase();
      const className = item.className?.toLowerCase() ?? '';
      const education =
        item.tuitionClasses?.[0]?.education?.toLowerCase() ?? '';
      const coachingType =
        formatArrayToString(
          item.tuitionClasses?.[0]?.coachingType,
        )?.toLowerCase() ?? '';
      const boardType =
        item.tuitionClasses?.[0]?.boardType?.toLowerCase() ?? '';
      const medium =
        formatArrayToString(item.tuitionClasses?.[0]?.medium)?.toLowerCase() ??
        '';
      const section =
        formatArrayToString(item.tuitionClasses?.[0]?.section)?.toLowerCase() ??
        '';
      const subject =
        formatArrayToString(item.tuitionClasses?.[0]?.subject)?.toLowerCase() ??
        '';
      const category = item.tuitionClasses?.[0]?.education?.toLowerCase() ?? '';

      const matchesSearch =
        lowerSearch === '' ||
        fullName.includes(lowerSearch) ||
        className.includes(lowerSearch) ||
        education.includes(lowerSearch);

      const matchesFilters =
        (filters.category
          ? category === filters.category.toLowerCase()
          : true) &&
        (filters.boardType
          ? boardType.includes(filters.boardType.toLowerCase())
          : true) &&
        (filters.medium
          ? medium.includes(filters.medium.toLowerCase())
          : true) &&
        (filters.section
          ? section.includes(filters.section.toLowerCase())
          : true) &&
        (filters.subject
          ? subject.includes(filters.subject.toLowerCase())
          : true) &&
        (filters.coachingType
          ? coachingType.includes(filters.coachingType.toLowerCase())
          : true);
      return matchesSearch && matchesFilters;
    });
    console.log('ClassDta', classData);
    setFilteredClassData(filtered);
    console.log(classData);
  }, [searchText, classData, filters]);

  const getClassList = async (pageNumber = 1) => {
    if (loading || !hasMoreData) {
      return;
    }

    setLoading(true);
    try {
      const res = await api.GetClassList.getClassList(pageNumber, 9);
      console.log(res);
      if (!res.ok) {
        console.log('API Error:', res.status, res.statusText);
        setHasMoreData(false);
        setLoading(false);
        return;
      }
      const text = await res.text();
      const jsondata = JSON.parse(text);
      console.log('RAW RESPONSE TEXT::', text);

      if (!text) {
        console.log('Empty response received');
        setHasMoreData(false);
        return;
      }

      const newData = jsondata.data;

      if (pageNumber === 1) {
        setClassData(newData);
      } else {
        setClassData(prev => [...(prev || []), ...newData]);
        console.log('CLASS DATA:::::::::::::::::', classData);
      }

      if (newData.length === 0) {
        console.log('last data', newData.lengt);

        setHasMoreData(false);
      } else {
        setPage(prev => prev + 1);
      }
    } catch (err) {
      console.log('ERROR IN GET CLASS LIST::', err);
    } finally {
      setLoading(false);
    }
  };

  const Profile = (classId: string) => {
    console.log('ON CLICK PROFILE');
    navigation.navigate('TutorProfile', {classId: classId});
  };

  const handleApplyFilters = (newFilters: typeof filters) => {
    setFilters(newFilters);
    setFilterModalVisible(false);
  };

  const handleCancelFilters = () => {
    setFilterModalVisible(false);
    setFilters({
      category: '',
      boardType: '',
      medium: '',
      section: '',
      subject: '',
      coachingType: '',
    });
  };

  const renderItem = ({item}: {item: any}) => {
    const profilePhoto = item.ClassAbout?.classesLogo;
    const imageUrl = profilePhoto ? `${imgBaseUrl}/${profilePhoto}` : null;
    const rating = item.averageRating ?? 'N/A';
    const reviewCount = item.reviewCount ?? 'N/A';
    const coachingType =
      formatArrayToString(
        item.tuitionClasses?.[0]?.coachingType,
      )?.toLowerCase() ?? '';

    return (
      <View
        style={[
          BaseStyle.itemContainer,
          {
            backgroundColor: isDarkMode ? '#1B1B1B' : PrimaryColors.WHITE,
            borderColor: isDarkMode ? '#3E3E3E' : '#CCCCCC',
          },
        ]}>
        <View style={[BaseStyle.imageWrapper, {width: '20%'}]}>
          <View style={BaseStyle.imageContainer}>
            {imageUrl ? (
              <Image
                source={{uri: imageUrl}}
                style={BaseStyle.image}
                resizeMode="cover"
              />
            ) : (
              <Icon name="person" size={50} color="#999" />
            )}
          </View>
        </View>
        <View style={[BaseStyle.infoWrapper, {width: '60%', marginLeft: '5%'}]}>
          <Text
            style={[
              BaseStyle.nameText,
              {color: isDarkMode ? PrimaryColors.WHITE : PrimaryColors.BLACK},
            ]}>
            {(item.firstName ?? '') + ' ' + (item.lastName ?? '')}
          </Text>
          <Text style={{fontSize: 14, color: '#A3A3B9'}}>{coachingType}</Text>
          <Text style={[BaseStyle.subText, {}]}>
            <Image
              resizeMode="contain"
              source={IMAGE_CONSTANT.STAR}
              style={{height: 14, width: 14}}
            />
            <View style={{flexDirection: 'row', paddingTop: 5, paddingLeft: 4}}>
              <View style={{backgroundColor: '#FFEBF0', borderRadius: 8}}>
                <Text
                  style={{
                    fontSize: 12,
                    color: '#A3A3B9',
                  }}>{` ${rating}.0 `}</Text>
              </View>
              <View style={{marginLeft: '2%'}}>
                <Text
                  style={{
                    fontSize: 12,
                    color: '#A3A3B9',
                  }}>{`(${reviewCount} reviews)`}</Text>
              </View>
            </View>
          </Text>
        </View>
        <View
          style={{justifyContent: 'center', width: '15%', paddingLeft: '5%'}}>
          <TouchableOpacity onPress={() => Profile(item.id)}>
            <Feather name="arrow-right" size={30} color={'#776D6D'} />
          </TouchableOpacity>
        </View>
      </View>
    );
  };

  return (
    <SafeAreaProvider
      style={[{backgroundColor: isDarkMode ? 'black' : 'white'}]}>
        <View style={{flexDirection: 'row',
    paddingHorizontal: '5%',
    width: '100%',
    backgroundColor: PrimaryColors.BLACK,
    height: 120}}>
              <View style={{justifyContent: 'center',
    height: '100%',
    flex: 1,}}>
                <Text style={{textAlign: 'center',
    fontSize: 24,
    color: PrimaryColors.WHITE,marginTop:8 }}>Search Tutor</Text>
              </View>
              <View style={{width: 34,
    borderColor:PrimaryColors.WHITE,justifyContent:'center'}} >
<TouchableOpacity onPress={() => setFilterModalVisible(true)}>
              <Image
                source={IMAGE_CONSTANT.FILTTER}
                style={BaseStyle.filterIcon}
                resizeMode="cover"
              />
            </TouchableOpacity>
            </View>
              <View style={{position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    height: 20,
    backgroundColor: isDarkMode ? PrimaryColors.BLACK : PrimaryColors.WHITE,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20}} />
            </View>
      <SafeAreaView style={{flex: 1, width: '100%',}} edges={['left','right']}>
        {/* <View
          style={{
            flexDirection: 'row',
            paddingHorizontal: '5%',
            width: '100%',
            marginTop:8
          }}>
          <View style={{width: '20%'}}>
          </View>
          <View style={{width: '60%'}}>
            <Text
              style={{
                textAlign: 'center',
                fontSize: 24,
                color: isDarkMode ? PrimaryColors.WHITE : PrimaryColors.BLACK,
              }}>
              Search Tutor
            </Text>
          </View>
          <View
            style={{
              width: '20%',
              alignItems: 'flex-end',
              justifyContent: 'center',
            }}>
            <TouchableOpacity onPress={() => setFilterModalVisible(true)}>
              {' '}
              <Image
                source={IMAGE_CONSTANT.FILTTER}
                style={BaseStyle.filterIcon}
                resizeMode="cover"
              />
            </TouchableOpacity>
          </View>
        </View> */}

        <View style={[{flex: 1 }]}>
          <View style={{flexDirection: 'row'}}>
            <View
              style={[
                BaseStyle.searchContainer,
                {
                  backgroundColor: isDarkMode ? '#1B1B1B' : 'white',
                  borderColor: isDarkMode ? '#3E3E3E' : '#CCCCCC',
                  // height:40,
                },
              ]}>
              <Ionicons
                name="search"
                style={{color: isDarkMode ? '#CCCCCC' : '#CCCCCC'}}
                size={24}
              />
              <TextInput
                style={{
                  height: '100%',
                  fontSize: 20,
                  paddingLeft: '3%',
                  color: isDarkMode ? PrimaryColors.WHITE : PrimaryColors.BLACK,
                  width:'90%',
                }}
                placeholder="Search Tutor"
                placeholderTextColor={isDarkMode ? '#FFFFFF' : '#CCCCCC'}
                value={searchText}
                onChangeText={setSearchText}
              />
            </View>
          </View>
          <ClassFilter
            title={'Filter Classes'}
            currentFilters={filters}
            onApply={handleApplyFilters}
            onCancel={handleCancelFilters}
            modalVisible={isFilterModalVisible}
            setModalVisible={setFilterModalVisible}
          />
          <View style={{marginTop: 8, marginBottom: 50}}>
            <FlatList
              data={filteredClassData}
              keyExtractor={item => item.id}
              renderItem={renderItem}
              contentContainerStyle={{padding: 16}}
              onEndReached={() => {
                if (!loading && hasMoreData) {
                  getClassList(page);
                }
              }}
              onEndReachedThreshold={0.5}
              ListFooterComponent={
                loading ? (
                  <View style={{paddingVertical: 16, alignItems: 'center'}}>
                    <ActivityIndicator
                      size="small"
                      color={
                        isDarkMode ? PrimaryColors.WHITE : PrimaryColors.BLACK
                      }
                    />
                  </View>
                ) : null
              }
            />
          </View>
        </View>
      </SafeAreaView>
    </SafeAreaProvider>
  );
};

export default ClassList;

const BaseStyle = StyleSheet.create({
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderRadius: 10,
    paddingHorizontal: 8,
    marginLeft: 16,
    // marginTop: 12,
    width: '90%',
    borderWidth: 1.5,
  },
  itemContainer: {
    flexDirection: 'row',
    borderRadius: 15,
    paddingHorizontal: 20,
    paddingVertical: 15,
    marginBottom: 12,
    alignItems: 'center',
    borderWidth: 1,
  },
  imageWrapper: {
    width: '25%',
    alignItems: 'center',
    justifyContent: 'center',
  },
  imageContainer: {
    height: 80,
    width: 80,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    overflow: 'hidden',
    backgroundColor: '#e6e6e6',
  },
  image: {
    height: '100%',
    width: '100%',
  },
  infoWrapper: {
    width: '50%',
    paddingLeft: 8,
    justifyContent: 'center',
  },
  nameText: {
    fontSize: 18,
    fontWeight: '700',
    marginBottom: 2,
  },
  filterIcon: {
    height: 25,
    width: 25,
    tintColor: PrimaryColors.WHITE,
    marginTop:8,

  },
  subText: {
    fontSize: 15,
    fontWeight: '500',
  },
  buttonWrapper: {
    width: '25%',
    justifyContent: 'center',
    alignItems: 'center',
  },
  button: {
    width: '90%',
    borderWidth: 1,
    borderRadius: 8,
    paddingVertical: 6,
    alignItems: 'center',
    borderColor: '#ccc',
    backgroundColor: PrimaryColors.LIGHTORANGE,
  },
  buttonText: {
    fontSize: 14,
    fontWeight: '600',
  },
});
