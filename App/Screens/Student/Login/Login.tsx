/* eslint-disable react-native/no-inline-styles */
import React, { useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  Image,
  TextInput,
} from 'react-native';
import Toast from 'react-native-simple-toast';
import { useNavigation } from '@react-navigation/native';
import IndexStyle from '../../../Theme/IndexStyle';
import { loginStudent, continueWithEmail } from '../../../services/authService';
import { SafeAreaProvider, SafeAreaView } from 'react-native-safe-area-context';
import { ScrollView } from 'react-native-gesture-handler';
import { ActivityIndicator } from 'react-native';

const Login = () => {
  const navigation = useNavigation<any>();
  const [phoneNumber, setPhoneNumber] = useState('');
  const [email, setEmail] = useState('');
  const [loginMethod, setLoginMethod] = useState<'otp' | 'email'>('otp');
  const [emailData, setEmailData] = useState<any>(null);
  const [authError, setAuthError] = useState('');
  const [emailLoading, setEmailLoading] = useState(false);
  const [loginLoading, setLoginLoading] = useState(false);
  const [emailChecked, setEmailChecked] = useState(false);

  const { isDarkMode } = IndexStyle();

  const handlePhoneLogin = async () => {
    if (!phoneNumber.trim()) {
      Toast.show('Please enter phone number', Toast.SHORT);
      return;
    }
    setLoginLoading(true);
    try {
      const loginData: any = { 
        contactNo: phoneNumber, 
        flowType: 'login' 
      };
      
      if (email && email.trim()) {
        loginData.email = email.trim();
      }
      
      await loginStudent(loginData);
      Toast.show('OTP sent to your number', Toast.SHORT);
      
      const otpParams: any = { 
        contactNo: phoneNumber, 
        flowType: 'login' 
      };
      
      if (email && email.trim()) {
        otpParams.email = email.trim();
      }
      
      navigation.navigate('OTP', otpParams);
    } catch (error: any) {
      const msg = error?.response?.data?.message || '';
      if (msg.toLowerCase().includes('not registered')) {
        Toast.show('This mobile number is not registered.', Toast.SHORT);
      } else {
        Toast.show(msg || 'Failed to send OTP', Toast.SHORT);
      }
    } finally {
      setLoginLoading(false);
    }
  };

  const handleEmailCheck = async () => {
    if (!email.trim()) {
      Toast.show('Please enter your email', Toast.SHORT);
      return;
    }
    setEmailLoading(true);
    setAuthError('');
    setEmailChecked(false);
    try {
      const response = await continueWithEmail({ email, flowType: 'login' });
      if (response.success === false && response.message === 'Email not registered. Please sign up.') {
        setEmailData(null);
        setEmailChecked(true);
        Toast.show('Please enter your mobile number to sign up', Toast.SHORT);
        setEmailLoading(false);
        return;
      }
      if (response.success === true && response.data) {
        setEmailData(response.data);
        setEmailChecked(true);
        if (response.data.contactNo && response.data.contactNo.trim() !== '') {
          Toast.show('OTP sent to your registered mobile', Toast.SHORT);
          navigation.navigate('OTP', { 
            contactNo: response.data.contactNo, 
            email, 
            flowType: 'login' 
          });
        } else {
          Toast.show('Please enter your mobile number to continue', Toast.SHORT);
        }
        setEmailLoading(false);
        return;
      }
      setAuthError(response.message || 'Email check failed');
      Toast.show(response.message || 'Email check failed', Toast.SHORT);
    } catch (error: any) {
      const errorMessage = error?.response?.data?.message || 'Something went wrong';
      setAuthError(errorMessage);
      Toast.show(errorMessage, Toast.SHORT);
    } finally {
      setEmailLoading(false);
    }
  };

  const handleMobileSubmit = async () => {
    if (!phoneNumber.trim()) {
      Toast.show('Please enter your mobile number', Toast.SHORT);
      return;
    }
    setLoginLoading(true);
    setAuthError('');
    try {
      const loginData: any = { 
        contactNo: phoneNumber, 
        flowType: 'login' 
      };
      
      if (email && email.trim()) {
        loginData.email = email.trim();
      }
      
      await loginStudent(loginData);
      Toast.show('OTP sent to your number', Toast.SHORT);
      
      const otpParams: any = { 
        contactNo: phoneNumber, 
        flowType: 'login' 
      };
      
      if (email && email.trim()) {
        otpParams.email = email.trim();
      }
      
      navigation.navigate('OTP', otpParams);
    } catch (error: any) {
      const backendMsg = error?.response?.data?.message;
      const errorMessage = backendMsg || 'Failed to send OTP. Please try again.';
      setAuthError(errorMessage);
      Toast.show(errorMessage, Toast.SHORT);
    } finally {
      setLoginLoading(false);
    }
  };

  const resetForm = () => {
    setEmailData(null);
    setAuthError('');
    setPhoneNumber('');
    setEmail('');
    setEmailChecked(false);
  };

  return (
    <SafeAreaProvider style={{ backgroundColor: isDarkMode ? '#0A0A0A' : '#FAFBFC' }}>
      <SafeAreaView style={{ flex: 1, backgroundColor: isDarkMode ? '#0A0A0A' : '#FAFBFC' }} edges={['top', 'left', 'right']}>
        <View style={{ flex: 1, backgroundColor: isDarkMode ? '#0A0A0A' : '#FAFBFC' }}>
          {/* Background Gradient */}
          <View
            style={{
              position: 'absolute',
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              backgroundColor: isDarkMode ? '#1A1A1A' : '#FD904B08',
            }}
          />
          
          {/* Decorative Elements */}
          <View style={{
            position: 'absolute',
            top: -100,
            right: -50,
            width: 200,
            height: 200,
            borderRadius: 100,
            backgroundColor: isDarkMode ? '#FD904B10' : '#FD904B08',
          }} />
          <View style={{
            position: 'absolute',
            top: 150,
            left: -80,
            width: 160,
            height: 160,
            borderRadius: 80,
            backgroundColor: isDarkMode ? '#FD904B05' : '#FD904B05',
          }} />

          <ScrollView
            contentContainerStyle={{ 
              flexGrow: 1, 
              alignItems: 'center', 
              paddingTop: 40, 
              paddingBottom: 20,
              paddingHorizontal: 24,
            }}
            keyboardShouldPersistTaps="handled"
            showsVerticalScrollIndicator={false}
          >
            {/* Logo and Header */}
            <View style={{
              alignItems: 'center',
              marginBottom: 32,
            }}>
              <View style={{
                width: 120,
                height: 120,
                borderRadius: 60,
                backgroundColor: isDarkMode ? '#1A1A1A' : '#FFFFFF',
                alignItems: 'center',
                justifyContent: 'center',
                shadowColor: '#000',
                shadowOffset: { width: 0, height: 8 },
                shadowOpacity: 0.12,
                shadowRadius: 24,
                elevation: 8,
                marginBottom: 24,
              }}>
                <Image
                  source={require('../../../Assets/Images/UestLogo.png')}
                  style={{ 
                    width: 80, 
                    height: 80, 
                    resizeMode: 'contain',
                  }}
                />
              </View>
              <Text style={{ 
                fontSize: 32, 
                fontWeight: '800', 
                color: isDarkMode ? '#FFFFFF' : '#1A1A1A', 
                letterSpacing: -0.5, 
                textAlign: 'center',
                marginBottom: 8,
              }}>
                Welcome Back
              </Text>
              <Text style={{ 
                fontSize: 16, 
                color: isDarkMode ? '#A0A0A0' : '#6B7280', 
                textAlign: 'center',
                lineHeight: 22,
              }}>
                Sign in to continue your learning journey
              </Text>
            </View>

            {/* Login Card */}
            <View style={{
              width: '100%',
              maxWidth: 400,
              backgroundColor: isDarkMode ? '#1A1A1A' : '#FFFFFF',
              borderRadius: 24,
              padding: 24,
              shadowColor: '#000',
              shadowOffset: { width: 0, height: 12 },
              shadowOpacity: 0.08,
              shadowRadius: 24,
              elevation: 8,
              borderWidth: 1,
              borderColor: isDarkMode ? '#2A2A2A' : '#F3F4F6',
            }}>
              {/* Method Toggle */}
              <View style={{ 
                flexDirection: 'row', 
                backgroundColor: isDarkMode ? '#2A2A2A' : '#F9FAFB',
                borderRadius: 16,
                padding: 4,
                marginBottom: 24,
              }}>
                <TouchableOpacity
                  style={{
                    flex: 1,
                    paddingVertical: 12,
                    paddingHorizontal: 16,
                    backgroundColor: loginMethod === 'otp' ? '#FD904B' : 'transparent',
                    borderRadius: 12,
                    alignItems: 'center',
                  }}
                  onPress={() => {
                    setLoginMethod('otp');
                    resetForm();
                  }}
                  activeOpacity={0.8}
                >
                  <Text style={{ 
                    color: loginMethod === 'otp' ? '#FFFFFF' : (isDarkMode ? '#A0A0A0' : '#6B7280'), 
                    fontWeight: '600', 
                    fontSize: 15 
                  }}>
                    Phone
                  </Text>
                </TouchableOpacity>
                <TouchableOpacity
                  style={{
                    flex: 1,
                    paddingVertical: 12,
                    paddingHorizontal: 16,
                    backgroundColor: loginMethod === 'email' ? '#FD904B' : 'transparent',
                    borderRadius: 12,
                    alignItems: 'center',
                  }}
                  onPress={() => {
                    setLoginMethod('email');
                    resetForm();
                  }}
                  activeOpacity={0.8}
                >
                  <Text style={{ 
                    color: loginMethod === 'email' ? '#FFFFFF' : (isDarkMode ? '#A0A0A0' : '#6B7280'), 
                    fontWeight: '600', 
                    fontSize: 15 
                  }}>
                    Email
                  </Text>
                </TouchableOpacity>
              </View>

              {/* Error Message */}
              {authError ? (
                <View style={{
                  backgroundColor: '#FEF2F2',
                  borderWidth: 1,
                  borderColor: '#FECACA',
                  borderRadius: 12,
                  padding: 16,
                  marginBottom: 20,
                  flexDirection: 'row',
                  alignItems: 'center',
                }}>
                  <View style={{
                    width: 20,
                    height: 20,
                    borderRadius: 10,
                    backgroundColor: '#EF4444',
                    alignItems: 'center',
                    justifyContent: 'center',
                    marginRight: 12,
                  }}>
                    <Text style={{ color: '#FFFFFF', fontSize: 12, fontWeight: 'bold' }}>!</Text>
                  </View>
                  <Text style={{
                    color: '#DC2626',
                    fontSize: 14,
                    fontWeight: '500',
                    flex: 1,
                  }}>
                    {authError}
                  </Text>
                </View>
              ) : null}

              {/* Phone Login Form */}
              {loginMethod === 'otp' ? (
                <>
                  <View style={{
                    flexDirection: 'row',
                    alignItems: 'center',
                    borderWidth: 1.5,
                    borderRadius: 16,
                    borderColor: isDarkMode ? '#3A3A3A' : '#E5E7EB',
                    backgroundColor: isDarkMode ? '#2A2A2A' : '#F9FAFB',
                    paddingHorizontal: 16,
                    paddingVertical: 16,
                    marginBottom: 24,
                  }}>
                    <View style={{
                      backgroundColor: isDarkMode ? '#3A3A3A' : '#F3F4F6',
                      paddingHorizontal: 12,
                      paddingVertical: 8,
                      borderRadius: 12,
                      marginRight: 12,
                    }}>
                      <Text style={{ 
                        fontSize: 16, 
                        fontWeight: '600', 
                        color: isDarkMode ? '#FFFFFF' : '#374151',
                      }}>
                        +91
                      </Text>
                    </View>
                    <TextInput
                      style={{ 
                        flex: 1, 
                        fontSize: 16, 
                        color: isDarkMode ? '#FFFFFF' : '#1F2937',
                        paddingVertical: 0,
                        fontWeight: '500',
                      }}
                      placeholder="Enter phone number"
                      placeholderTextColor={isDarkMode ? '#6B7280' : '#9CA3AF'}
                      value={phoneNumber}
                      onChangeText={setPhoneNumber}
                      keyboardType="phone-pad"
                      maxLength={10}
                    />
                  </View>
                  <TouchableOpacity
                    style={{
                      backgroundColor: '#FD904B',
                      borderRadius: 16,
                      paddingVertical: 18,
                      alignItems: 'center',
                      shadowColor: '#FD904B',
                      shadowOffset: { width: 0, height: 8 },
                      shadowOpacity: 0.25,
                      shadowRadius: 16,
                      elevation: 8,
                    }}
                    onPress={handlePhoneLogin}
                    activeOpacity={0.85}
                    disabled={loginLoading}>
                    <Text style={{ 
                      color: '#FFFFFF', 
                      fontSize: 18, 
                      fontWeight: '700', 
                      letterSpacing: 0.5 
                    }}>
                      {loginLoading ? 'Sending OTP...' : 'Continue with Phone'}
                    </Text>
                  </TouchableOpacity>
                </>
              ) : (
                <>
                  {/* Email Input */}
                  <View style={{
                    borderWidth: 1.5,
                    borderRadius: 16,
                    borderColor: isDarkMode ? '#3A3A3A' : '#E5E7EB',
                    backgroundColor: isDarkMode ? '#2A2A2A' : '#F9FAFB',
                    paddingHorizontal: 16,
                    paddingVertical: 16,
                    marginBottom: 24,
                  }}>
                    <TextInput
                      style={{ 
                        fontSize: 16, 
                        color: isDarkMode ? '#FFFFFF' : '#1F2937',
                        paddingVertical: 0,
                        fontWeight: '500',
                      }}
                      placeholder="Enter email address"
                      placeholderTextColor={isDarkMode ? '#6B7280' : '#9CA3AF'}
                      value={email}
                      onChangeText={setEmail}
                      keyboardType="email-address"
                      autoCapitalize="none"
                    />
                  </View>

                  {/* Phone Input (when needed) */}
                  {emailChecked && (!emailData?.contactNo || emailData.contactNo?.trim() === '') && (
                    <View style={{
                      flexDirection: 'row',
                      alignItems: 'center',
                      borderWidth: 1.5,
                      borderRadius: 16,
                      borderColor: isDarkMode ? '#3A3A3A' : '#E5E7EB',
                      backgroundColor: isDarkMode ? '#2A2A2A' : '#F9FAFB',
                      paddingHorizontal: 16,
                      paddingVertical: 16,
                      marginBottom: 24,
                    }}>
                      <View style={{
                        backgroundColor: isDarkMode ? '#3A3A3A' : '#F3F4F6',
                        paddingHorizontal: 12,
                        paddingVertical: 8,
                        borderRadius: 12,
                        marginRight: 12,
                      }}>
                        <Text style={{ 
                          fontSize: 16, 
                          fontWeight: '600', 
                          color: isDarkMode ? '#FFFFFF' : '#374151',
                        }}>
                          +91
                        </Text>
                      </View>
                      <TextInput
                        style={{ 
                          flex: 1, 
                          fontSize: 16, 
                          color: isDarkMode ? '#FFFFFF' : '#1F2937',
                          paddingVertical: 0,
                          fontWeight: '500',
                        }}
                        placeholder="Enter phone number"
                        placeholderTextColor={isDarkMode ? '#6B7280' : '#9CA3AF'}
                        value={phoneNumber}
                        onChangeText={setPhoneNumber}
                        keyboardType="phone-pad"
                        maxLength={10}
                      />
                    </View>
                  )}

                  {/* Email Action Button */}
                  <TouchableOpacity
                    style={{
                      backgroundColor: '#FD904B',
                      borderRadius: 16,
                      paddingVertical: 18,
                      alignItems: 'center',
                      shadowColor: '#FD904B',
                      shadowOffset: { width: 0, height: 8 },
                      shadowOpacity: 0.25,
                      shadowRadius: 16,
                      elevation: 8,
                    }}
                    onPress={() => {
                      if (emailChecked && (!emailData?.contactNo || emailData.contactNo?.trim() === '')) {
                        handleMobileSubmit();
                      } else {
                        handleEmailCheck();
                      }
                    }}
                    activeOpacity={0.85}
                    disabled={emailLoading || loginLoading}>
                    <Text style={{ 
                      color: '#FFFFFF', 
                      fontSize: 18, 
                      fontWeight: '700', 
                      letterSpacing: 0.5 
                    }}>
                      {emailLoading ? 'Checking Email...' : loginLoading ? 'Sending OTP...' : emailChecked && (!emailData?.contactNo || emailData.contactNo?.trim() === '') ? 'Update Contact & Send OTP' : 'Continue with Email'}
                    </Text>
                  </TouchableOpacity>
                </>
              )}

              {/* Loading Indicator */}
              {(emailLoading || loginLoading) && (
                <View style={{
                  marginTop: 20,
                  alignItems: 'center',
                }}>
                  <ActivityIndicator
                    size="large"
                    color="#FD904B"
                  />
                </View>
              )}
            </View>

            {/* Terms and Sign Up */}
            <View style={{
              alignItems: 'center',
              marginTop: 24,
            }}>
              <Text style={{
                fontSize: 13,
                color: isDarkMode ? '#A0A0A0' : '#6B7280',
                textAlign: 'center',
                lineHeight: 18,
                marginBottom: 24,
              }}>
                By signing in, you agree to our
                <Text style={{ color: '#FD904B', fontWeight: '600' }}> Terms</Text> &
                <Text style={{ color: '#FD904B', fontWeight: '600' }}> Privacy Policy</Text>
              </Text>
              
              <TouchableOpacity 
                style={{ 
                  paddingVertical: 8,
                  paddingHorizontal: 16,
                }} 
                onPress={() => navigation.navigate('SignUp')}
              >
                <Text style={{ 
                  color: isDarkMode ? '#A0A0A0' : '#6B7280', 
                  fontSize: 15, 
                  textAlign: 'center' 
                }}>
                  Don&apos;t have an account? <Text style={{ color: '#FD904B', fontWeight: '700' }}>Sign Up</Text>
                </Text>
              </TouchableOpacity>
            </View>
          </ScrollView>
        </View>
      </SafeAreaView>
    </SafeAreaProvider>
  );
};

export default Login;