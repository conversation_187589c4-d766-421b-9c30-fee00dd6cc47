/* eslint-disable jsx-quotes */
/* eslint-disable react-native/no-inline-styles */
/* eslint-disable quotes */
import React, {useEffect, useRef} from 'react';
import {useState} from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  Image,
  StyleSheet,
  Dimensions,
} from 'react-native';
import {useNavigation} from '@react-navigation/native';
import {SafeAreaProvider, SafeAreaView} from 'react-native-safe-area-context';
import {ScrollView} from 'react-native-gesture-handler';
import IndexStyle from '../../../Theme/IndexStyle';

const {height, width} = Dimensions.get('window');

const WelcomeScreen = () => {
  const navigation = useNavigation<any>();
  const {isDarkMode} = IndexStyle();

  const slides = [
    {
      image: require('../../../Assets/Images/UestLogo.png'),
      title: 'Join With <PERSON>',
      text: `Less<PERSON>, you'll love Guaranteed.`,
    },
    {
      image: require('../../../Assets/Images/U-Whiz-Exam.png'),
      title: 'U-Whiz',
      text: `U Whiz – Super Kids Exam is live!`,
    },
  ];

  const scrollRef = useRef<ScrollView>(null);
  const [currentIndex, setCurrentIndex] = useState(0);

  // Auto scroll logic
  useEffect(() => {
    const interval = setInterval(() => {
      const nextIndex = (currentIndex + 1) % slides.length;
      scrollRef.current?.scrollTo({x: nextIndex * width, animated: true});
      setCurrentIndex(nextIndex);
    }, 3000);

    return () => clearInterval(interval);
  }, [currentIndex, slides.length]);

  // Update index on manual scroll
  const handleScroll = (event: any) => {
    const offsetX = event.nativeEvent.contentOffset.x;
    const newIndex = Math.round(offsetX / width);
    setCurrentIndex(newIndex);
  };

  const handleSignIn = () => {
    navigation.navigate('Login');
  };

  const handleCreateAccount = () => {
    navigation.navigate('SignUp');
  };

  return (
    <SafeAreaProvider
      style={{
        backgroundColor: isDarkMode ? '#000000' : '#FFFFFF',
      }}>
      <SafeAreaView style={styles.container}>
        {/* Slider Section */}
        <View style={styles.sliderContainer}>
          <ScrollView
            horizontal
            pagingEnabled
            showsHorizontalScrollIndicator={false}
            onScroll={handleScroll}
            scrollEventThrottle={16}
            ref={scrollRef}>
            {slides.map((slide, index) => (
              <View key={index} style={styles.slide}>
                <Image source={slide.image} style={styles.slideImage} />
                <Text style={[styles.slideTitle, {color: isDarkMode ? '#FFFFFF' : '#000000'}]}>
                  {slide.title}
                </Text>
                <Text style={styles.slideText}>
                  {slide.text}
                </Text>
              </View>
            ))}
          </ScrollView>

          {/* Slider Indicators */}
          <View style={styles.indicatorsContainer}>
            {slides.map((_, index) => (
              <View
                key={index}
                style={[
                  styles.indicator,
                  {
                    width: currentIndex === index ? 30 : 8,
                    backgroundColor: currentIndex === index ? '#FD904B' : '#D9D9D9',
                  },
                ]}
              />
            ))}
          </View>
        </View>

        {/* Buttons Section */}
        <View style={styles.buttonsContainer}>
          <TouchableOpacity
            style={[styles.primaryButton, {backgroundColor: '#FD904B'}]}
            onPress={handleSignIn}
            activeOpacity={0.8}>
            <Text style={styles.primaryButtonText}>Sign In</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[
              styles.secondaryButton,
              {
                backgroundColor: 'transparent',
                borderColor: isDarkMode ? '#FFFFFF' : '#000000',
                borderWidth: 2,
              },
            ]}
            onPress={handleCreateAccount}
            activeOpacity={0.8}>
            <Text style={[styles.secondaryButtonText, {color: isDarkMode ? '#FFFFFF' : '#000000'}]}>
              Create Account
            </Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    </SafeAreaProvider>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  sliderContainer: {
    flex: 1,
    justifyContent: 'center',
  },
  slide: {
    width: width,
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 20,
  },
  slideImage: {
    width: '80%',
    height: height * 0.25,
    resizeMode: 'contain',
    marginBottom: 30,
  },
  slideTitle: {
    fontSize: 28,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 10,
    color: '#000000',
  },
  slideText: {
    fontSize: 18,
    fontWeight: '500',
    textAlign: 'center',
    color: '#656565',
    lineHeight: 24,
  },
  indicatorsContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 30,
    marginBottom: 20,
  },
  indicator: {
    height: 6,
    borderRadius: 3,
    marginHorizontal: 4,
  },
  buttonsContainer: {
    paddingHorizontal: 30,
    paddingBottom: 50,
    gap: 16,
  },
  primaryButton: {
    width: '100%',
    paddingVertical: 16,
    borderRadius: 12,
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  primaryButtonText: {
    color: '#FFFFFF',
    fontSize: 18,
    fontWeight: '600',
  },
  secondaryButton: {
    width: '100%',
    paddingVertical: 16,
    borderRadius: 12,
    alignItems: 'center',
    justifyContent: 'center',
  },
  secondaryButtonText: {
    fontSize: 18,
    fontWeight: '600',
  },
});

export default WelcomeScreen; 