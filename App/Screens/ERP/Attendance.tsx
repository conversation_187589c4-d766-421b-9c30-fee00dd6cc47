import React, { useCallback, useEffect, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
  TouchableOpacity,
} from 'react-native';
import { CalendarList } from 'react-native-calendars';
import { useNavigation } from '@react-navigation/native';
import IndexStyle from '../../Theme/IndexStyle';
import { PrimaryColors } from '../../Utils/Constants';
import { SafeAreaProvider } from 'react-native-safe-area-context';
import CurvHeader from '../../CommonComponents/CurvHeader';
import {
  getAttendance,
  getDisciplineIssue,
} from '../../services/studentClassesService';
import moment from 'moment';
const RANGE = 6;
const initialDate = moment().format('YYYY-MM-DD');
export interface AttendanceData {
  selected: boolean;
  selectedColor: string;
}

export interface CalenderDate {
  dateString: string;
  day: number;
  month: number;
  timestamp: number;
  year: number;
}

const Attendance = () => {
  const navigation = useNavigation();
  const { isDarkMode } = IndexStyle();
  const [disciplineIssue, setDisciplineIssue] = useState({});
  const [marked, setMarked] = useState({});
  const [statusCounts, setStatusCounts] = useState({
    absent: 0,
    leave: 0,
    present: 0,
    discipline: 0,
  });
  const fetchAttendanceData = async (startDate: string, endDate: string) => {
    try {
      const { data } = await getAttendance(startDate, endDate);
      const newMarkedDates: { [date: string]: AttendanceData } = {};
      let counts = {
        absent: 0,
        leave: 0,
        present: 0,
        discipline: 0,
      };
      Object.keys(data).forEach((date: string) => {
        const { status } = data[date];
        let selectedColor = '';
        if (status === 'present') {
          selectedColor = 'green';
          counts.present += 1;
        } else if (status === 'absent') {
          selectedColor = 'red';
          counts.absent += 1;
        } else if (status === 'leave') {
          selectedColor = 'orange';
          counts.leave += 1;
        }

        newMarkedDates[date] = {
          selected: true,
          selectedColor: selectedColor,
        };
      });
      setStatusCounts(counts);
      setMarked(newMarkedDates);
    } catch (error) {
      console.error('Error fetching attendance data:', error.response);
    }
  };

  const onMonthChange = useCallback((month: CalenderDate) => {
    const { year, month: currentMonth } = month;
    const startDate = moment()
      .year(year)
      .month(currentMonth - 1)
      .startOf('month')
      .format('YYYY-MM-DD');
    const endDate = moment()
      .year(year)
      .month(currentMonth - 1)
      .endOf('month')
      .format('YYYY-MM-DD');

    fetchAttendanceData(startDate, endDate);
    fetchDisciplineIssue(startDate, endDate);
  }, []);

  useEffect(() => {
    const startDate = moment().startOf('month').format('YYYY-MM-DD');
    const endDate = moment().endOf('month').format('YYYY-MM-DD');
    fetchAttendanceData(startDate, endDate);
    fetchDisciplineIssue(startDate, endDate);
  }, []);

  const fetchDisciplineIssue = async (startDate: string, endDate: string) => {
    try {
      const { data } = await getDisciplineIssue(startDate, endDate);
      setDisciplineIssue(data);
      console.log(data);
      setStatusCounts(prevCounts => ({
        ...prevCounts,
        discipline: Object.keys(data).length,
      }));
    } catch (error) {
      console.error('Error fetching attendance data:', error);
    }
  };

  return (
    <SafeAreaProvider>
      <SafeAreaView
        style={{
          flex: 1,
          backgroundColor: isDarkMode ? '#000000' : PrimaryColors.WHITE,
        }}
        edges={['left', 'right']}>
        <CurvHeader
          title="Attendance"
          isBack={true}
          onBackPress={() => {
            navigation.goBack();
          }}
        />
        <View
          style={[
            styles.calendarContainer,
            { backgroundColor: isDarkMode ? '#000000' : PrimaryColors.WHITE },
          ]}>
          <CalendarList
            current={initialDate}
            pastScrollRange={RANGE}
            futureScrollRange={RANGE}
            onMonthChange={onMonthChange}
            calendarHeight={390}
            horizontal={true}
            pagingEnabled={true}
            staticHeader={true}
            markedDates={marked}
            theme={{
              backgroundColor: isDarkMode ? '#000000' : '#ffffff',
              calendarBackground: isDarkMode ? '#1f1b1bff' : '#ffffff',
              textSectionTitleColor: isDarkMode ? '#aaaaaa' : '#333333',
              selectedDayBackgroundColor: '#FF914D',
              selectedDayTextColor: '#ffffff',
              todayTextColor: '#FF914D',
              dayTextColor: isDarkMode ? '#e0e0e0' : '#2d4150',
              textDisabledColor: isDarkMode ? '#555555' : '#d9e1e8',
              selectedDotColor: '#ffffff',
              arrowColor: isDarkMode ? '#ffffff' : '#000000',
              monthTextColor: isDarkMode ? '#ffffff' : '#000000',
              textDayFontWeight: '500',
              textMonthFontWeight: 'bold',
              textDayHeaderFontWeight: '600',
              textDayFontSize: 14,
              textMonthFontSize: 16,
              textDayHeaderFontSize: 12,
            }}
            style={styles.calendar}
          />
        </View>

        {/* Summary Section */}
        <View style={styles.summaryContainer}>
          {/* Absent */}
          <TouchableOpacity
            style={[
              styles.summaryItem,
              {
                backgroundColor: '#FDEAEA',
                borderLeftColor: '#B71C1C',
                borderColor: '#B71C1C',
                borderWidth: 0.5,
              },
            ]}>
            <Text style={styles.summaryText}>Absent</Text>
            <View style={[styles.countContainer, { borderLeftColor: '#B71C1C' }]}>
              <Text style={styles.count}>{statusCounts.absent}</Text>
            </View>
          </TouchableOpacity>

          {/* Leave */}
          <TouchableOpacity
            style={[
              styles.summaryItem,
              {
                backgroundColor: '#FFF8E1',
                borderLeftColor: '#FBC02D',
                borderColor: '#F9A825',
                borderWidth: 0.5,
              },
            ]}>
            <Text style={styles.summaryText}>Leave</Text>
            <View style={[styles.countContainer, { borderLeftColor: '#F9A825' }]}>
              <Text style={styles.count}>{statusCounts.leave}</Text>
            </View>
          </TouchableOpacity>

          {/* Present */}
          <TouchableOpacity
            style={[
              styles.summaryItem,
              {
                backgroundColor: '#E8F5E9',
                borderLeftColor: '#388E3C',
                borderColor: '#1B5E20',
                borderWidth: 0.5,
              },
            ]}>
            <Text style={styles.summaryText}>Present</Text>
            <View style={[styles.countContainer, { borderLeftColor: '#43A047' }]}>
              <Text style={styles.count}>{statusCounts.present}</Text>
            </View>
          </TouchableOpacity>

          {/* Discipline */}
          <TouchableOpacity
            onPress={() =>
              navigation.navigate('Discipline', {
                title: 'Discipline Issue',
                info: disciplineIssue,
              })
            }
            style={[
              styles.summaryItem,
              {
                backgroundColor: '#E3F2FD',
                borderLeftColor: '#1976D2',
                borderColor: '#0D47A1',
                borderWidth: 0.5,
              },
            ]}>
            <Text style={styles.summaryText}>Discipline</Text>
            <View style={[styles.countContainer, { borderLeftColor: '#1E88E5' }]}>
              <Text style={styles.count}>{statusCounts.discipline}</Text>
            </View>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    </SafeAreaProvider>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: PrimaryColors.WHITE,
  },
  calendarContainer: {
    margin: 8,
    overflow: 'hidden',
  },
  calendar: {
    borderRadius: 20,
  },
  summaryContainer: {
    marginHorizontal: 16,
    marginTop: 32,
  },
  summaryItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderRadius: 20,
    marginBottom: 10,
    borderLeftWidth: 4,
    shadowColor: '#000',
    shadowOffset: { width: -8, height: 0 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
  },
  summaryText: {
    fontSize: 16,
    fontWeight: '500',
    color: '#000',
  },
  countContainer: {
    width: 30,
    height: 30,
    borderRadius: 15,
    borderWidth: 2,
    justifyContent: 'center',
    alignItems: 'center',
  },
  count: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#000',
  },
});

export default Attendance;
