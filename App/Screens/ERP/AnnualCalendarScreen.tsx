import React, {useEffect, useState} from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  ActivityIndicator,
  SafeAreaView,
} from 'react-native';
import {Calendar} from 'react-native-calendars';
import moment from 'moment';
import CurvHeader from '../../CommonComponents/CurvHeader';
import {SafeAreaProvider} from 'react-native-safe-area-context';
import {PrimaryColors} from '../../Utils/Constants';
import IndexStyle from '../../Theme/IndexStyle';
import {useNavigation} from '@react-navigation/native';
import {getAnnualCalender} from '../../services/studentClassesService';

interface CalendarEvent {
  title: string;
  start: string;
  end: string;
  color: string;
  cid: string;
  type: string;
}

const AnnualCalendarScreen = () => {
  const [events, setEvents] = useState<CalendarEvent[]>([]);
  const [markedDates, setMarkedDates] = useState<any>({});
  const [selectedDate, setSelectedDate] = useState<string | null>(null);
  const [loading, setLoading] = useState(true);
  const {isDarkMode} = IndexStyle();
  const navigation = useNavigation();

  const fetchCalendarData = async () => {
    try {
      const response = await getAnnualCalender();
      const data: CalendarEvent[] = response;

      setEvents(data);
      const marks: any = {};

      data.forEach(event => {
        const date = moment(event.start).format('YYYY-MM-DD');
        if (!marks[date]) {
          marks[date] = {
            dots: [{color: event.color}],
          };
        } else {
          const existingColors = marks[date].dots.map(dot => dot.color);
          if (!existingColors.includes(event.color)) {
            marks[date].dots.push({color: event.color});
          }
        }
      });

      setMarkedDates(marks);
    } catch (error) {
      console.error('Failed to fetch calendar data:', error?.response || error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchCalendarData();
  }, []);

  const eventsOnDate = (date: string) => {
    const formattedDate = moment(date).format('YYYY-MM-DD');
    return events.filter(
      event => moment(event.start).format('YYYY-MM-DD') === formattedDate,
    );
  };

  return (
    <SafeAreaProvider>
      <SafeAreaView
        style={[
          styles.safeArea,
          {backgroundColor: isDarkMode ? '#000000' : PrimaryColors.WHITE},
        ]}>
        <CurvHeader
          title="Annual Calendar"
          isBack
          onBackPress={() => navigation.goBack()}
        />
        <View
          style={[
            styles.container,
            {backgroundColor: isDarkMode ? '#000000' : '#fff'},
          ]}>
          {loading ? (
            <ActivityIndicator
              size="large"
              style={{marginTop: 20}}
              color="#FF914D"
            />
          ) : (
            <>
              <Calendar
                markedDates={{
                  ...markedDates,
                  ...(selectedDate && {
                    [selectedDate]: {
                      ...(markedDates[selectedDate] || {}),
                      selected: true,
                      selectedColor: '#FF914D',
                    },
                  }),
                }}
                markingType={'multi-dot'}
                onDayPress={day => setSelectedDate(day.dateString)}
                theme={{
                  backgroundColor: isDarkMode ? '#000000' : '#ffffff',
                  calendarBackground: isDarkMode ? '#000000' : '#ffffff',
                  textSectionTitleColor: isDarkMode ? '#aaaaaa' : '#333333',
                  selectedDayBackgroundColor: '#FF914D',
                  selectedDayTextColor: '#ffffff',
                  todayTextColor: '#FF914D',
                  dayTextColor: isDarkMode ? '#e0e0e0' : '#2d4150',
                  textDisabledColor: isDarkMode ? '#555555' : '#d9e1e8',
                  dotColor: '#FF914D',
                  selectedDotColor: '#ffffff',
                  arrowColor: isDarkMode ? '#ffffff' : '#000000',
                  monthTextColor: isDarkMode ? '#ffffff' : '#000000',
                  textDayFontWeight: '500',
                  textMonthFontWeight: 'bold',
                  textDayHeaderFontWeight: '600',
                  textDayFontSize: 14,
                  textMonthFontSize: 16,
                  textDayHeaderFontSize: 12,
                }}
              />

              <ScrollView style={styles.eventList}>
                {selectedDate && eventsOnDate(selectedDate).length > 0 && (
                  <View style={styles.eventsSection}>
                    <Text
                      style={[
                        styles.eventsHeading,
                        {color: isDarkMode ? '#fff' : '#000'},
                      ]}>
                      Events on {selectedDate}
                    </Text>
                    {eventsOnDate(selectedDate).map((event, index) => (
                      <View
                        key={index}
                        style={[
                          styles.eventCard,
                          {
                            borderLeftColor: event.color,
                            backgroundColor: isDarkMode ? '#222' : '#f8f8f8',
                          },
                        ]}>
                        <Text
                          style={[
                            styles.eventTitle,
                            {color: isDarkMode ? '#fff' : '#000'},
                          ]}>
                          {event.title?.trim() ? event.title : 'Untitled Event'}
                        </Text>
                        <Text
                          style={[
                            styles.eventDate,
                            {color: isDarkMode ? '#aaa' : '#555'},
                          ]}>
                          {moment(event.start).format('MMM D, YYYY')}
                        </Text>
                        <View style={styles.legendContainer}>
                          <View style={styles.legendItem}>
                            <View
                              style={[
                                styles.legendDot,
                                {backgroundColor: event.color},
                              ]}
                            />
                            <Text
                              style={[
                                styles.legendLabel,
                                {color: isDarkMode ? '#ccc' : '#555'},
                              ]}>
                              {event.type}
                            </Text>
                          </View>
                        </View>
                      </View>
                    ))}
                  </View>
                )}
                {selectedDate && eventsOnDate(selectedDate).length === 0 && (
                  <Text
                    style={[
                      styles.noEvents,
                      {color: isDarkMode ? '#999' : '#666'},
                    ]}>
                    No events on {selectedDate}
                  </Text>
                )}
              </ScrollView>
            </>
          )}
        </View>
      </SafeAreaView>
    </SafeAreaProvider>
  );
};

export default AnnualCalendarScreen;

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
  },
  container: {
    flex: 1,
  },
  eventList: {
    flex: 1,
    paddingHorizontal: 16,
    paddingVertical: 10,
  },
  eventsHeading: {
    fontSize: 18,
    fontWeight: '700',
    marginBottom: 12,
  },
  eventCard: {
    padding: 14,
    borderLeftWidth: 4,
    borderRadius: 12,
    marginBottom: 14,
  },
  eventTitle: {
    fontWeight: '600',
    fontSize: 16,
    marginBottom: 6,
  },
  eventDate: {
    fontSize: 13,
  },
  noEvents: {
    textAlign: 'center',
    marginTop: 30,
    fontSize: 14,
  },
  legendContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 10,
  },
  legendItem: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  legendDot: {
    width: 10,
    height: 10,
    borderRadius: 5,
    marginRight: 6,
  },
  legendLabel: {
    fontSize: 13,
  },
});
