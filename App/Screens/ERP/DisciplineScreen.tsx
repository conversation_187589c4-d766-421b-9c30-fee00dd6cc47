import React from 'react';
import {
  View,
  SafeAreaView,
  ScrollView,
  StyleSheet,
  Text,
} from 'react-native';
import {useNavigation, useRoute} from '@react-navigation/native';
import {SafeAreaProvider} from 'react-native-safe-area-context';
import CurvHeader from '../../CommonComponents/CurvHeader';
import {PrimaryColors} from '@Utils/Constants';
import IndexStyle from '../../Theme/IndexStyle';
import { dateFormat } from '../../Utils/Helper';

interface Discipline {
  date: Date;
  id: number;
  discipline_issue: string;
}

export interface Params {
  key: string;
  name: string;
  params: {
    title: string;
    info?: any;
  };
  path: string;
}

const DisciplineScreen = () => {
  const {params}: Params = useRoute();
  const navigation = useNavigation();
  const {isDarkMode} = IndexStyle();

  const data = params?.info ?? [];

  const colors = {
    background: isDarkMode ? '#000000' : PrimaryColors.WHITE,
    cardBackground: isDarkMode ? '#1E1E1E' : '#FFFFFF',
    border: isDarkMode ? '#333' : '#ccc',
    textPrimary: isDarkMode ? '#FFFFFF' : '#000000',
    textSecondary: isDarkMode ? '#AAAAAA' : '#333333',
    shadowColor: isDarkMode ? '#000' : '#000',
  };

  return (
    <SafeAreaProvider>
      <SafeAreaView style={[styles.safeArea, {backgroundColor: colors.background}]}>
        <CurvHeader
          title="Discipline Issue"
          isBack
          onBackPress={() => navigation.goBack()}
        />
        <ScrollView contentContainerStyle={styles.content}>
          {data.length === 0 ? (
            <Text style={[styles.noDataText, {color: colors.textSecondary}]}>
              No Data Found
            </Text>
          ) : (
            data.map((item: Discipline) => (
              <View style={styles.cardContainer} key={item.id}>
                <View
                  style={[
                    styles.card,
                    {
                      backgroundColor: colors.cardBackground,
                      shadowColor: colors.shadowColor,
                    },
                  ]}>
                  <View style={[styles.cardRow, {borderBottomColor: colors.border}]}>
                    <Text style={[styles.label, {color: colors.textSecondary}]}>
                      Date:
                    </Text>
                    <Text style={[styles.value, {color: colors.textPrimary}]}>
                      {dateFormat(item.date)}
                    </Text>
                  </View>
                  <View style={[styles.cardRow, {borderBottomColor: colors.border}]}>
                    <Text style={[styles.label, {color: colors.textSecondary}]}>
                      Issue:
                    </Text>
                    <Text style={[styles.value, {color: colors.textPrimary}]}>
                      {item.discipline_issue}
                    </Text>
                  </View>
                </View>
              </View>
            ))
          )}
        </ScrollView>
      </SafeAreaView>
    </SafeAreaProvider>
  );
};

export {DisciplineScreen};

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
  },
  content: {
    paddingHorizontal: 16,
    paddingBottom: 24,
    marginTop : 20
  },
  noDataText: {
    fontSize: 16,
    textAlign: 'center',
    marginTop: 40,
  },
  cardContainer: {
    marginBottom: 16,
  },
  card: {
    borderRadius: 12,
    padding: 16,
    elevation: 3,
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.15,
    shadowRadius: 4,
  },
  cardRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingVertical: 8,
    borderBottomWidth: 0.5,
  },
  label: {
    fontSize: 14,
    fontWeight: '600',
  },
  value: {
    fontSize: 14,
    fontWeight: '500',
    flex: 1,
    textAlign: 'right',
    paddingLeft: 10,
  },
});