/* eslint-disable quotes */
/* eslint-disable react-native/no-inline-styles */
import React from 'react';
import {View, Text, StyleSheet, Image, TouchableOpacity} from 'react-native';
import {IMAGE_CONSTANT, PrimaryColors} from '../../Utils/Constants';
import {useNavigation} from '@react-navigation/native';
import {SafeAreaProvider, SafeAreaView} from 'react-native-safe-area-context';
import Icon from 'react-native-vector-icons/MaterialIcons';

const UseAppAs = () => {
  const navigation = useNavigation<any>();

  const OnClickStudent = () => {
    console.log('NAVIGATE AS PER STUDENT FLOW');
    navigation.navigate('Login');
  };

  return (
    <SafeAreaProvider style={{backgroundColor: '#f0d7c7'}}>
      <SafeAreaView
        style={{backgroundColor: PrimaryColors.WHITE, height: '100%'}}>
        <View
          style={{
            height: '70%',
            width: '100%',
            transform: [{scaleX: 2}],
            borderBottomStartRadius: '60%',
            borderBottomEndRadius: '60%',
            overflow: 'hidden',
          }}>
          <View
            style={{
              flex: 1,
              transform: [{scaleX: 0.5}],
              backgroundColor: '#F0D7C7',
              alignItems: 'center',
              justifyContent: 'center',
            }}>
            <View
              style={{
                position: 'absolute',
                top: '20%',
                alignSelf: 'center',
                bottom: 100,
              }}>
              <Text style={[BaseStyle.mainText, {color: PrimaryColors.BLACK}]}>
                Pick Your Role
              </Text>
              <Text style={[BaseStyle.mainText, {color: PrimaryColors.BLACK}]}>
                To
              </Text>
              <Text style={BaseStyle.mainText}>Continue</Text>
            </View>

            <Image
              resizeMode="contain"
              source={IMAGE_CONSTANT.USEAPPASILLUS}
              style={{
                height: 370,
                width: 370,
                marginTop: '75%',
              }}
            />
          </View>
        </View>

        <View
          style={{
            position: 'absolute',
            bottom: 60,
            marginLeft: 16,
            marginRight: 16,
            width: '90%',
            alignSelf: 'center',
          }}>
          <TouchableOpacity
            activeOpacity={0.8}
            onPressIn={() => {}}
            onPressOut={() => {}}
            onPress={() => OnClickStudent()}
            style={{
              height: 50,
              width: '100%',
              borderColor: PrimaryColors.BLACK,
              borderRadius: 14,
              justifyContent: 'center',
              alignItems: 'center',
              backgroundColor: PrimaryColors.ORANGE,
              marginBottom: '6%',
            }}>
            <View style={{flexDirection: 'row'}}>
              <Icon
                name="school"
                size={24}
                color={PrimaryColors.WHITE}
                style={[{marginRight: 5}]}
              />
              <Text
                style={[BaseStyle.buttonText, {color: PrimaryColors.WHITE}]}>
                Student
              </Text>
            </View>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    </SafeAreaProvider>
  );
};

export default UseAppAs;

const BaseStyle = StyleSheet.create({
  logo: {
    width: '70%',
    height: 100,
  },
  cardstyle: {
    width: 200,
    height: 160,
    marginTop: 32,
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 15,
  },
  cardImage: {
    height: 90,
    width: 90,
  },
  mainText: {
    fontSize: 32,
    fontWeight: 'bold',
    color: '#FF914D',
    textAlign: 'center',
  },
  button: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    paddingHorizontal: 20,
    borderRadius: 25,
  },
  icon: {
    marginRight: 10,
  },
  buttonText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#fff',
    marginLeft: 4,
  },
  parent: {
    height: '80%',
    width: '100%',
    transform: [{scaleX: 2}],
    borderBottomStartRadius: 200,
    borderBottomEndRadius: 200,
    overflow: 'hidden',
  },
  child: {
    flex: 1,
    transform: [{scaleX: 0.5}],
    backgroundColor: 'yellow',
    alignItems: 'center',
    justifyContent: 'center',
  },
});
